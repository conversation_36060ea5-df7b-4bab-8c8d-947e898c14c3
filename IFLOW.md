# iFlow CLI 项目上下文文档

## 项目概述

这是一个基于AI技术的小红书UGC内容营销分析系统，专门用于分析小红书平台的用户生成内容(UGC)数据，为品牌营销人员、内容创作者和数据分析师提供深度洞察和策略建议。

### 核心功能
- **Excel数据导入**：支持上传和解析小红书软文数据Excel文件
- **智能数据分析**：自动分析内容表现、用户互动模式和创作者价值
- **AI深度洞察**：基于通义千问大模型的内容语义分析和情感分析
- **可视化展示**：丰富的图表和仪表板展示分析结果
- **策略建议**：基于分析结果提供可执行的优化建议

## 技术架构

### 技术栈
- **前端框架**：Next.js 15 (React 19)
- **UI库**：Tailwind CSS 4
- **图表库**：Recharts 3.1.2
- **图标库**：Lucide React 0.542.0
- **数据处理**：xlsx 0.18.5
- **AI服务**：通义千问(Qwen) API
- **语言**：TypeScript

### 系统架构
采用前后端一体化架构，所有数据处理在客户端完成，不上传服务器，确保数据隐私安全。

## 项目结构

```
xhs-analytics/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── api/               # API路由
│   │   └── page.tsx           # 主页面
│   ├── components/            # React组件
│   │   ├── ui/               # 基础UI组件
│   │   │   ├── FileUpload.tsx      # 文件上传组件
│   │   │   ├── DataFlowProgress.tsx # 进度展示组件
│   │   │   └── tabs.tsx            # 标签页组件
│   │   └── analytics/        # 分析相关组件
│   │       ├── Dashboard.tsx      # 数据仪表板
│   │       └── OnDemandAI.tsx     # 按需AI分析
│   ├── services/             # 业务逻辑服务
│   │   ├── dataFlow.ts       # 数据流管理服务
│   │   └── ai.ts            # AI服务调用
│   ├── types/               # TypeScript类型定义
│   └── utils/               # 工具函数
├── public/                  # 静态资源
├── package.json            # 项目依赖配置
├── .env.local             # 环境变量配置
└── README.md              # 项目说明文档
```

## 核心模块

### 1. 数据处理模块
- **FileUpload组件**：支持Excel文件(.xlsx)解析，数据验证和基础清洗
- **DataFlowManager服务**：管理数据处理流程，包括数据清洗、基础分析、创作者分析、内容分类等

### 2. AI服务模块
- **AIService类**：管理通义千问API调用，支持任务队列、缓存机制、错误重试
- **HybridAnalyticsEngine**：结合规则引擎和AI分析的混合分析引擎

### 3. 可视化展示模块
- **Dashboard组件**：综合展示关键指标和分析结果
- **OnDemandAI组件**：用户触发的深度AI分析，支持洞察、推荐、策略三种分析类型

## 快速开始

### 环境要求
- Node.js 18+
- npm 或 yarn

### 安装依赖
```bash
cd xhs-analytics
npm install
```

### 环境配置
在项目根目录创建 `.env.local` 文件：
```env
# 通义千问(Qwen) API 配置
QWEN_API_KEY=your-api-key-here
QWEN_MODEL_NAME=qwen-max
QWEN_API_ENDPOINT=https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation

# 其他配置
NEXT_PUBLIC_APP_NAME=XHS Analytics
```

### 启动开发服务器
```bash
npm run dev
```
访问 [http://localhost:3000](http://localhost:3000)

### 构建和部署
```bash
npm run build    # 构建生产版本
npm start        # 启动生产服务器
npm run lint     # 代码检查
```

## 数据格式要求

### 必需字段
- 笔记链接：小红书笔记的URL
- 软文标题：笔记标题
- 软文作者：笔记作者昵称
- 软文内容：笔记正文内容
- 发文日期：笔记发布时间
- 发文地点：笔记发布地点

### 数据字段
- 点赞数：笔记获得的点赞数量
- 收藏数：笔记被收藏的数量
- 评论数：笔记收到的评论数量
- 评论：用户评论内容列表
- 软文标签：笔记关联的标签
- 小红书号：作者的小红书ID
- IP属地：作者发布时的IP属地
- 关注：作者被关注数量
- 粉丝：作者粉丝数量
- 获赞与收藏：作者累计获赞和收藏数

## 分析维度

### 基础分析
- 总笔记数、点赞数、收藏数、评论数等核心指标
- 平均互动数据
- Top表现内容识别

### 创作者分析
- 高价值创作者识别
- 创作者商业价值评分
- 创作者粉丝影响力分析

### 内容分类
- 教程类、体验类、推荐类、攻略类、生活类、其他
- 各类别内容表现对比

### AI深度分析
- 内容语义分析
- 用户情感分析
- 商业价值评估
- 趋势预测
- 个性化推荐

## 开发指南

### 组件开发规范
- 使用TypeScript进行类型安全开发
- 遵循React Hooks模式
- 组件职责单一，可复用性强
- 使用Tailwind CSS进行样式设计

### 数据流设计
1. 用户上传Excel文件
2. FileUpload组件解析并传递数据
3. DataFlowManager处理数据流程
4. 执行基础规则引擎分析
5. 结果展示在Dashboard中
6. 用户可触发AI深度分析

### AI集成最佳实践
- 使用模板化提示词设计
- 实现结果缓存避免重复调用
- 支持任务队列和并发控制
- 提供错误重试机制

## 注意事项

1. **API密钥安全**：确保通义千问API密钥安全存储，不要提交到版本控制
2. **数据处理限制**：建议单次处理数据量不超过10万条记录
3. **网络要求**：AI分析功能需要稳定的网络连接
4. **隐私保护**：所有数据处理在客户端完成，不上传服务器
5. **性能优化**：大数据集采用分批处理，避免浏览器内存溢出

## 故障排除

### 常见问题
1. **API调用失败**：检查网络连接和API密钥配置
2. **数据解析错误**：确保Excel文件格式正确，包含必需字段
3. **内存溢出**：减少单次处理的数据量
4. **构建失败**：检查Node.js版本和依赖安装

### 调试工具
- 浏览器开发者工具查看网络请求
- 控制台日志查看详细错误信息
- 使用提供的测试脚本验证功能

## 扩展开发

### 添加新分析维度
1. 在types/index.ts中定义新的数据类型
2. 在DataFlowManager中添加新的分析方法
3. 创建对应的UI组件展示结果

### 集成新的AI模型
1. 在AIService中添加新的模型配置
2. 创建对应的提示词模板
3. 实现结果解析逻辑

### 自定义可视化
1. 使用Recharts库创建新的图表组件
2. 在Dashboard或专用分析组件中集成
3. 确保响应式设计适配不同屏幕