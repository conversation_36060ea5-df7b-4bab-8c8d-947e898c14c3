// 测试通义千问API情感分析接口
require('dotenv').config();

const API_KEY = process.env.QWEN_API_KEY || 'sk-676513654fba4a318e64501772deae87';
const MODEL_NAME = process.env.QWEN_MODEL_NAME || 'qwen-max';
const API_ENDPOINT = process.env.QWEN_API_ENDPOINT || 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation';

// 测试数据：模拟小红书评论
const testComments = [
  '这个产品真的很好用，强烈推荐给大家！',
  '质量一般，价格有点贵，不太值得购买',
  '绝了！yyds！质量太赞了！',
  '笑死，这什么玩意儿，完全没用',
  '还可以吧，没有特别的感觉',
  '破防了，这质量也太差了吧',
  '挺好的，符合预期，会继续购买',
  '无语了，这钱花得太冤枉了',
  '精致好看，质量很棒，很喜欢！',
  '踩雷了，大家别买，真的垃圾'
];

async function testSentimentAPI() {
  console.log('=== 测试通义千问情感分析API ===');
  console.log('API Key:', API_KEY ? '已设置' : '未设置');
  console.log('模型:', MODEL_NAME);
  console.log('接口:', API_ENDPOINT);
  console.log('');

  const sentimentPrompt = `请分析以下小红书评论的情感倾向，统计积极、消极、中性的数量和比例。

评论内容：
${testComments.join('\n')}

请严格按照以下JSON格式返回，不要添加任何其他文字或解释：
{
  "positive": 积极评论数量,
  "negative": 消极评论数量, 
  "neutral": 中性评论数量,
  "total": 总评论数,
  "positiveRatio": 积极比例(0-1之间的小数),
  "negativeRatio": 消极比例(0-1之间的小数),
  "neutralRatio": 中性比例(0-1之间的小数)
}

分析标准：
- 积极：表达满意、喜欢、推荐、赞扬等正面情绪
- 消极：表达不满、失望、批评、抱怨等负面情绪  
- 中性：客观描述、事实陈述、无明显情感倾向`;

  console.log('发送的提示词:');
  console.log(sentimentPrompt);
  console.log('');

  try {
    const response = await fetch(API_ENDPOINT, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${API_KEY}`
      },
      body: JSON.stringify({
        model: MODEL_NAME,
        input: {
          messages: [{
            role: 'user',
            content: sentimentPrompt
          }]
        },
        parameters: {
          result_format: 'message'
        }
      })
    });

    console.log('HTTP响应状态:', response.status, response.statusText);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ API调用失败:', errorText);
      return;
    }

    const result = await response.json();
    console.log('📤 完整API响应:');
    console.log(JSON.stringify(result, null, 2));
    console.log('');

    const content = result.output?.choices?.[0]?.message?.content;
    
    console.log('=== AI返回内容分析 ===');
    console.log('原始内容:');
    console.log(content);
    console.log('');
    console.log('内容类型:', typeof content);
    console.log('内容长度:', content?.length || 0);
    console.log('');

    if (!content) {
      console.error('❌ AI返回内容为空');
      return;
    }

    // 尝试解析JSON
    console.log('=== JSON解析测试 ===');
    let jsonContent = content.trim();
    console.log('修剪后的内容:', jsonContent);
    console.log('');

    // 检查是否是markdown代码块格式
    const jsonMatch = jsonContent.match(/```json\s*([\s\S]*?)\s*```/);
    if (jsonMatch) {
      console.log('✅ 检测到markdown代码块格式');
      jsonContent = jsonMatch[1].trim();
      console.log('提取的JSON内容:', jsonContent);
      console.log('');
    } else {
      console.log('❌ 未检测到markdown代码块格式');
      console.log('');
    }

    // 尝试解析JSON
    try {
      const sentimentResult = JSON.parse(jsonContent);
      console.log('✅ JSON解析成功!');
      console.log('解析结果:', sentimentResult);
      console.log('');

      // 计算比例
      const total = sentimentResult.total || sentimentResult.positive + sentimentResult.negative + sentimentResult.neutral;
      const positiveRatio = sentimentResult.positiveRatio || sentimentResult.positive / total;
      const negativeRatio = sentimentResult.negativeRatio || sentimentResult.negative / total;
      const neutralRatio = sentimentResult.neutralRatio || sentimentResult.neutral / total;

      console.log('=== 最终结果 ===');
      console.log('积极评论数:', sentimentResult.positive);
      console.log('消极评论数:', sentimentResult.negative);
      console.log('中性评论数:', sentimentResult.neutral);
      console.log('总评论数:', total);
      console.log('积极比例:', positiveRatio.toFixed(3));
      console.log('消极比例:', negativeRatio.toFixed(3));
      console.log('中性比例:', neutralRatio.toFixed(3));

    } catch (parseError) {
      console.error('❌ JSON解析失败:', parseError.message);
      console.error('尝试解析的内容:', jsonContent);
      
      // 尝试修复常见的JSON格式问题
      console.log('=== 尝试修复JSON格式 ===');
      
      // 尝试修复：省略小数点前的0
      let fixedContent = jsonContent.replace(/:\s*\.(\d+)/g, ': 0.$1');
      console.log('修复小数格式后的内容:', fixedContent);
      
      // 尝试修复：缺少引号
      fixedContent = fixedContent.replace(/([{,]\s*)([a-zA-Z_]+)(\s*):/g, '$1"$2"$3:');
      console.log('修复引号后的内容:', fixedContent);
      
      try {
        const fixedResult = JSON.parse(fixedContent);
        console.log('✅ 修复后JSON解析成功!');
        console.log('修复后的结果:', fixedResult);
      } catch (secondError) {
        console.error('❌ 修复后仍然解析失败:', secondError.message);
      }
    }

  } catch (error) {
    console.error('❌ 请求过程出错:', error.message);
  }
}

// 运行测试
testSentimentAPI();