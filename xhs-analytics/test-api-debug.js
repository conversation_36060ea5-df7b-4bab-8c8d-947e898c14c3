const { default: fetch } = require('node-fetch');
require('dotenv').config({ path: '.env.local' });

async function testAPI() {
  const apiKey = process.env.QWEN_API_KEY;
  const apiEndpoint = process.env.QWEN_API_ENDPOINT;
  const modelName = 'qwen-turbo';
  
  console.log('🔧 API配置信息:');
  console.log('- API Key:', apiKey ? `${apiKey.substring(0, 10)}...` : '未设置');
  console.log('- API Endpoint:', apiEndpoint);
  console.log('- Model Name:', modelName);
  console.log('');
  
  const testPrompt = `请分析以下小红书评论的情感倾向，按照严格的JSON格式返回结果。

评论内容：
好香
超喜欢牛杂啊啊啊啊！下次一定要试试
想去！！！

请务必严格按照以下JSON格式返回，不要添加任何解释文字、markdown标记或其他内容：
{
  "positive": 积极评论数量,
  "negative": 消极评论数量,
  "neutral": 中性评论数量,
  "total": 总评论数,
  "positiveRatio": 积极比例(0到1之间的小数),
  "negativeRatio": 消极比例(0到1之间的小数),
  "neutralRatio": 中性比例(0到1之间的小数)
}

分析标准：
- 积极：好、棒、赞、喜欢、推荐、不错、很棒等正面表达
- 消极：差、烂、不好、失望、不推荐、质量差等负面表达
- 中性：一般描述、事实陈述、无明显情感倾向

要求：只返回纯JSON格式，不要添加任何其他文字。`;

  try {
    console.log('📤 发送API请求...');
    const response = await fetch(apiEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify({
        model: modelName,
        input: {
          messages: [{
            role: 'user',
            content: testPrompt
          }]
        },
        parameters: {
          result_format: 'message',
          temperature: 0.1,
          top_p: 0.8,
          max_tokens: 500
        }
      })
    });

    console.log('📥 API响应状态:', response.status, response.statusText);
    console.log('📥 响应头:', Object.fromEntries(response.headers.entries()));
    
    const responseText = await response.text();
    console.log('📥 响应内容长度:', responseText.length);
    console.log('📥 响应内容前500字符:');
    console.log(responseText.substring(0, 500));
    console.log('');
    
    if (!response.ok) {
      console.error('❌ API调用失败');
      return;
    }
    
    try {
      const data = JSON.parse(responseText);
      console.log('✅ JSON解析成功');
      console.log('📊 解析后的数据:', JSON.stringify(data, null, 2));
      
      // 提取内容
      const content = data.output?.choices?.[0]?.message?.content || data.output?.text || '';
      console.log('📝 提取的内容:', content);
      
    } catch (parseError) {
      console.error('❌ JSON解析失败:', parseError.message);
    }
    
  } catch (error) {
    console.error('❌ 请求失败:', error.message);
  }
}

testAPI();
