          // 确保比例总和为1
          const sum = positive + negative + neutral;
          if (sum > 0) {
            positive = positive / sum;
            negative = negative / sum;
            neutral = neutral / sum;
          }
          
          console.log(`📊 最终情感分析结果: 积极${(positive * 100).toFixed(1)}% 消极${(negative * 100).toFixed(1)}% 中性${(neutral * 100).toFixed(1)}%`);
          
          return { positive, negative, neutral };
          
        } catch (parseError) {
          const errorMessage = parseError instanceof Error ? parseError.message : 'Unknown error';
          console.error(`❌ AI返回内容解析失败: ${errorMessage}`);
          console.error(`原始内容: ${content}`);
          console.error(`内容长度: ${content.length}`);
          
          // 最后的回退：使用关键词匹配
          console.warn('🔄 回退到关键词匹配分析');
          // 恢复原始模型设置
          this.aiService['modelName'] = originalModel;
          return this.fallbackSentimentAnalysis(posts);
        }
      }
      
      // 如果AI分析失败，回退到关键词匹配
      return this.fallbackSentimentAnalysis(posts);
      
    } catch (error) {
      // 恢复原始模型设置
      try {
        this.aiService['modelName'] = originalModel;
      } catch (e) {
        // 忽略恢复错误
      }
      console.warn('AI情感分析失败，使用关键词匹配:', error);
      // AI分析失败时回退到关键词匹配
      return this.fallbackSentimentAnalysis(posts);
    }
  }
  
  // 关键词匹配的回退方案
  private fallbackSentimentAnalysis(posts: XHSPost[]): { positive: number; negative: number; neutral: number } {
    const positiveKeywords = ['好', '棒', '赞', '喜欢', '爱', '优秀', '完美', '推荐', '不错', '很棒', '厉害', '学习', '有用', '感谢', '分享', '支持', '加油', '期待', '好看', '美丽', '精致', '质量好'];
    const negativeKeywords = ['差', '烂', '垃圾', '骗', '假', '失望', '后悔', '不好', '糟糕', '坑', '骗人', '虚假', '夸张', '不推荐', '别买', '踩雷', '质量差', '不值', '浪费钱', '退货', '差评', '吐槽', '无语'];
    
    let positiveCount = 0;
    let negativeCount = 0;
    let neutralCount = 0;
    
