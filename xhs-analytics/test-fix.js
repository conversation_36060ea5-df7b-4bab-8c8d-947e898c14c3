// 测试修复效果：模拟真实数据格式
const testComments = [
  "['好香'",
  "'超喜欢牛杂啊啊啊啊！下次一定要试试'",
  "'想去！！！'",
  "'码住'",
  "'牛杂好吃'",
  "'mark住了'",
  "'好想吃'"
];

const testComments2 = [
  "['🔥Bi 试三巨头： ❶ 高希霸55周年 🇨🇺 高希霸一直都是王者般的存在，松木+皮革+雪松…无压力~ 🥳有配置了独立雪茄房！随时品味一番~ 距离酒店不远~游玩打卡肯定要来这里体验一下😊'",
  "'@小籽珺'",
  "'不错'"
];

// 模拟修复函数
function cleanComments(comments) {
  return comments.map(comment => {
    // 确保评论是字符串
    let commentStr = String(comment || '').trim();
    
    // 如果评论包含数组语法（如 ['评论']），移除数组符号
    if (commentStr.startsWith("['") && commentStr.endsWith("']")) {
      commentStr = commentStr.slice(2, -2); // 移除 [' 和 ']
    } else if (commentStr.startsWith('["') && commentStr.endsWith('"]')) {
      commentStr = commentStr.slice(2, -2); // 移除 [" 和 "]
    } else if (commentStr.startsWith("'") && commentStr.endsWith("'")) {
      commentStr = commentStr.slice(1, -1); // 移除单引号
    } else if (commentStr.startsWith('"') && commentStr.endsWith('"')) {
      commentStr = commentStr.slice(1, -1); // 移除双引号
    }
    
    // 处理特殊情况：只有开头的 [' 但没有结尾的 ']
    if (commentStr.startsWith("['") && !commentStr.endsWith("']")) {
      commentStr = commentStr.slice(2); // 只移除开头的 ['
    }
    
    return commentStr;
  }).filter(comment => comment.length > 0);
}

console.log('=== 测试修复效果 ===');
console.log('');

console.log('📦 测试数据1（原始）:');
console.log(testComments);
console.log('');

console.log('🧹 测试数据1（修复后）:');
const cleaned1 = cleanComments(testComments);
console.log(cleaned1);
console.log('');

console.log('📝 提示词格式1:');
const prompt1 = `请分析以下小红书评论的情感倾向：

评论内容：
${cleaned1.slice(0, 5).join('\n')}

请返回JSON格式：
{
  "positive": 数量,
  "negative": 数量,
  "neutral": 数量
}`;
console.log(prompt1);
console.log('');

console.log('📦 测试数据2（原始）:');
console.log(testComments2);
console.log('');

console.log('🧹 测试数据2（修复后）:');
const cleaned2 = cleanComments(testComments2);
console.log(cleaned2);
console.log('');

console.log('📝 提示词格式2:');
const prompt2 = `请分析以下小红书评论的情感倾向：

评论内容：
${cleaned2.slice(0, 3).join('\n')}

请返回JSON格式：
{
  "positive": 数量,
  "negative": 数量,
  "neutral": 数量
}`;
console.log(prompt2);
console.log('');

console.log('✅ 修复完成！现在评论数据是纯文本格式，移除了数组符号。');
