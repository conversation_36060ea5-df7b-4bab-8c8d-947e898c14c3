// 测试修复后的情感分析功能
const { DataFlowManager } = require('./src/services/dataFlow.ts');

// 模拟测试数据
const testPosts = [
  {
    软文标题: '测试笔记1',
    软文作者: '测试用户1',
    软文内容: '这是测试内容',
    点赞数: 100,
    收藏数: 50,
    评论数: 20,
    粉丝: 1000,
    评论: [
      '好香',
      '超喜欢牛杂啊啊啊啊！下次一定要试试',
      '想去！！！',
      '码住',
      '牛杂好吃',
      'mark住了',
      '好想吃',
      '@橘子🍊',
      '很不错',
      'ge'
    ]
  },
  {
    软文标题: '测试笔记2',
    软文作者: '测试用户2',
    软文内容: '这是第二个测试内容',
    点赞数: 80,
    收藏数: 40,
    评论数: 15,
    粉丝: 800,
    评论: [
      '🔥Bi 试三巨头： ❶ 高希霸55周年 🇨🇺 高希霸一直都是王者般的存在，松木+皮革+雪松…无压力~ 🥳有配置了独立雪茄房！随时品味一番~ 距离酒店不远~游玩打卡肯定要来这里体验一下😊',
      '@小籽珺',
      '不错',
      '很棒的体验'
    ]
  }
];

async function testSentimentFix() {
  console.log('🧪 测试修复后的AI情感分析功能');
  console.log('================================');

  try {
    const dataFlowManager = new DataFlowManager();
    
    // 直接测试情感分析函数
    console.log('📊 开始测试情感分析...');
    const sentimentResult = await dataFlowManager.analyzeCommentSentiment(testPosts);
    
    console.log('\n✅ 情感分析结果:');
    console.log('积极情感:', (sentimentResult.positive * 100).toFixed(1) + '%');
    console.log('消极情感:', (sentimentResult.negative * 100).toFixed(1) + '%');
    console.log('中性情感:', (sentimentResult.neutral * 100).toFixed(1) + '%');
    
    // 验证结果合理性
    const total = sentimentResult.positive + sentimentResult.negative + sentimentResult.neutral;
    console.log('\n🔍 验证结果:');
    console.log('比例总和:', total.toFixed(3));
    console.log('是否接近1.0:', Math.abs(total - 1.0) < 0.01 ? '✅ 通过' : '❌ 失败');
    
    if (sentimentResult.positive > 0 && sentimentResult.negative >= 0 && sentimentResult.neutral >= 0) {
      console.log('✅ 情感分析修复成功！');
      return true;
    } else {
      console.log('❌ 情感分析结果不正确');
      return false;
    }
    
  } catch (error) {
    console.error('❌ 测试过程中出错:', error.message);
    return false;
  }
}

// 运行测试
if (require.main === module) {
  testSentimentFix().then(success => {
    if (success) {
      console.log('\n🎉 所有测试通过！AI情感分析功能已修复。');
    } else {
      console.log('\n❌ 测试失败，需要进一步检查。');
    }
    process.exit(success ? 0 : 1);
  });
}

module.exports = { testSentimentFix };