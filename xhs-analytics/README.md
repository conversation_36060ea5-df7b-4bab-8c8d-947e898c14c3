# 小红书UGC内容营销分析系统

基于AI技术的小红书内容深度分析和营销策略建议系统。

## 功能特性

### 🚀 核心功能
- **Excel数据导入**：支持上传和解析小红书软文数据Excel文件
- **智能数据分析**：自动分析内容表现、用户互动模式和创作者价值
- **AI深度洞察**：基于通义千问大模型的内容语义分析和情感分析
- **可视化展示**：丰富的图表和仪表板展示分析结果
- **策略建议**：基于分析结果提供可执行的优化建议

### 📊 分析维度
- **基础分析**：总笔记数、点赞数、收藏数、评论数等核心指标
- **创作者分析**：识别高价值创作者，分析其内容特点和表现
- **内容分类**：按内容类型(教程、体验、评测等)进行分类统计
- **互动分析**：分析用户互动行为模式
- **商业价值评估**：综合评估内容和创作者的商业合作潜力

### 🤖 AI分析功能
- **深度洞察**：分析内容表现模式和用户互动趋势
- **创作者推荐**：识别高价值创作者和合作机会
- **策略建议**：提供内容营销和投放策略

## 技术栈

- **前端框架**：Next.js 15 (React 19)
- **UI库**：Tailwind CSS
- **图表库**：Recharts
- **图标库**：Lucide React
- **数据处理**：xlsx
- **AI服务**：通义千问(Qwen) API

## 快速开始

### 环境要求
- Node.js 18+ 
- npm 或 yarn

### 安装依赖
```bash
npm install
```

### 环境配置
在项目根目录创建 `.env.local` 文件：

```env
# 通义千问(Qwen) API 配置
QWEN_API_KEY=your-api-key-here
QWEN_MODEL_NAME=qwen-max
QWEN_API_ENDPOINT=https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation

# 其他配置
NEXT_PUBLIC_APP_NAME=XHS Analytics
```

### 启动开发服务器
```bash
npm run dev
```

访问 [http://localhost:3000](http://localhost:3000) 查看应用。

### 构建生产版本
```bash
npm run build
npm start
```

## 使用说明

### 1. 数据上传
- 点击上传区域或拖拽Excel文件到指定区域
- 支持 `.xlsx` 和 `.xls` 格式
- 确保Excel文件包含必要的列：笔记链接、软文标题、软文作者、软文内容等

### 2. 数据分析
- 系统自动处理上传的数据并进行基础分析
- 查看"数据仪表板"了解基础统计信息
- 使用"AI深度分析"获取更深入的洞察

### 3. 结果解读
- **数据概览**：查看核心指标和top表现内容
- **创作者排行**：识别高价值创作者
- **内容分类**：了解不同类型内容的表现
- **AI洞察**：获取深度分析和策略建议

## 数据格式要求

### 必需字段
- 笔记链接：小红书笔记的URL
- 软文标题：笔记标题
- 软文作者：笔记作者昵称
- 软文内容：笔记正文内容
- 发文日期：笔记发布时间
- 发文地点：笔记发布地点

### 数据字段
- 点赞数：笔记获得的点赞数量
- 收藏数：笔记被收藏的数量
- 评论数：笔记收到的评论数量
- 评论：用户评论内容列表(逗号分隔)
- 软文标签：笔记关联的标签(逗号分隔)
- 小红书号：作者的小红书ID
- IP属地：作者发布时的IP属地
- 关注：作者被关注数量
- 粉丝：作者粉丝数量
- 获赞与收藏：作者累计获赞和收藏数

## 项目结构

```
src/
├── app/                    # Next.js App Router
│   ├── api/               # API路由
│   └── page.tsx           # 主页面
├── components/            # React组件
│   ├── ui/               # 基础UI组件
│   └── analytics/        # 分析相关组件
├── services/             # 业务逻辑服务
├── types/               # TypeScript类型定义
└── utils/               # 工具函数
```

## 开发说明

### 组件架构
- **FileUpload**：文件上传和解析组件
- **DataFlowProgress**：数据处理进度展示
- **Dashboard**：数据仪表板和基础分析
- **OnDemandAI**：按需AI分析组件
- **DataFlowManager**：数据流管理服务
- **AIService**：AI服务调用管理

### 数据流设计
1. 用户上传Excel文件
2. FileUpload组件解析并传递数据
3. DataFlowManager处理数据流程
4. 执行基础规则引擎分析
5. 结果展示在Dashboard中
6. 用户可触发AI深度分析

### AI集成
- 使用通义千问API进行深度分析
- 支持任务队列和并发控制
- 结果缓存和错误重试机制
- 模板化提示词设计

## 注意事项

- 请确保在阿里云控制台申请通义千问API密钥
- 数据在客户端处理，不上传到服务器
- 建议上传的数据量不超过10万条记录
- AI分析功能需要网络连接

## 许可证

MIT License