  private async analyzeCommentSentiment(posts: XHSPost[]): Promise<{ positive: number; negative: number; neutral: number }> {
    const allComments = posts.flatMap(post => post.评论 || []);
    
    console.log('🔍 analyzeCommentSentiment 调试信息:');
    console.log('- 处理的posts数量:', posts.length);
    console.log('- 提取的comments数量:', allComments.length);
    console.log('- 前3条评论:', allComments.slice(0, 3));
    console.log('');
    
    if (allComments.length === 0) {
      console.log('⚠️ 评论为空，返回默认值');
      return { positive: 0, negative: 0, neutral: 1 };
    }
    
    // 🔧 简化评论处理：统一转为字符串，并清理格式
    const cleanedComments = allComments.map(comment => {
      // 统一转为字符串
      let commentStr = String(comment || '').trim();
      
      // 清理各种格式问题
      // 移除数组括号和引号：['评论'] → 评论
      if (commentStr.startsWith("['") && commentStr.endsWith("']")) {
        commentStr = commentStr.slice(2, -2);
      } else if (commentStr.startsWith('["') && commentStr.endsWith('"]')) {
        commentStr = commentStr.slice(2, -2);
      } else if (commentStr.startsWith("'") && commentStr.endsWith("'")) {
        commentStr = commentStr.slice(1, -1);
      } else if (commentStr.startsWith('"') && commentStr.endsWith('"')) {
        commentStr = commentStr.slice(1, -1);
      }
      
      // 处理不完整的格式，如 "['好香'" 或 "'好香" 等
      if (commentStr.startsWith("['")) {
        commentStr = commentStr.slice(2); // 移除 ['
      } else if (commentStr.startsWith("'")) {
        commentStr = commentStr.slice(1); // 移除 '
      } else if (commentStr.startsWith('"')) {
        commentStr = commentStr.slice(1); // 移除 "
      }
      
      if (commentStr.endsWith("']")) {
        commentStr = commentStr.slice(0, -2); // 移除 ']
      } else if (commentStr.endsWith("'")) {
        commentStr = commentStr.slice(0, -1); // 移除 '
      } else if (commentStr.endsWith('"')) {
        commentStr = commentStr.slice(0, -1); // 移除 "
      }
      
      return commentStr;
    }).filter(comment => comment.length > 0);
    
    console.log('🧹 清理后的comments数量:', cleanedComments.length);
    console.log('🧹 清理后的前3条评论:', cleanedComments.slice(0, 3));
    console.log('');
    
    // 临时使用更快的模型进行情感分析
    const originalModel = this.aiService['modelName'];
    this.aiService['modelName'] = 'qwen-turbo'; // 使用更快的模型
    
    try {
      // 使用现有的AI服务进行情感分析，使用更快的模型
