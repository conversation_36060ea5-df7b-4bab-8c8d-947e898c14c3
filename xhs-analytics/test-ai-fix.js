// 测试AI情感分析修复效果
console.log('🧪 AI情感分析修复测试');
console.log('========================');

const testCases = [
  {
    name: "正常JSON格式",
    content: '{"positive": 5, "negative": 2, "neutral": 3, "total": 10, "positiveRatio": 0.5, "negativeRatio": 0.2, "neutralRatio": 0.3}'
  },
  {
    name: "Markdown代码块格式",
    content: "```json\n{\n  \"positive\": 5,\n  \"negative\": 2,\n  \"neutral\": 3,\n  \"total\": 10,\n  \"positiveRatio\": 0.5,\n  \"negativeRatio\": 0.2,\n  \"neutralRatio\": 0.3\n}\n```"
  },
  {
    name: "带引号的JSON",
    content: '"{\"positive\": 5, \"negative\": 2, \"neutral\": 3, \"total\": 10, \"positiveRatio\": 0.5, \"negativeRatio\": 0.2, \"neutralRatio\": 0.3}"'
  },
  {
    name: "非JSON格式",
    content: "这是一个普通的文本内容，不是JSON格式"
  }
];

// 模拟修复函数
function parseAIResponse(content) {
  try {
    console.log(`\n🔍 测试案例: ${testCases.find(tc => tc.content === content)?.name}`);
    console.log(`原始内容: ${content}`);
    console.log(`内容长度: ${content.length}`);
    
    // 尝试解析AI返回的JSON结果
    // 有时候AI会返回markdown格式的代码块，需要提取
    let jsonContent = content.trim();
    
    // 如果是markdown代码块格式，提取JSON部分
    const jsonMatch = jsonContent.match(/```json\s*([\s\S]*?)\s*```/);
    if (jsonMatch) {
      jsonContent = jsonMatch[1].trim();
      console.log(`📝 提取markdown代码块: ${jsonContent}`);
    }
    
    // 如果是引号包裹的JSON，移除引号
    if (jsonContent.startsWith('"') && jsonContent.endsWith('"')) {
      jsonContent = jsonContent.slice(1, -1);
      console.log(`📝 移除引号: ${jsonContent}`);
    }
    
    // 处理转义字符
    jsonContent = jsonContent.replace(/\"/g, '"');
    console.log(`📝 处理转义字符: ${jsonContent}`);
    
    // 尝试解析JSON
    const sentimentResult = JSON.parse(jsonContent);
    console.log(`✅ JSON解析成功:`, sentimentResult);
    
    return {
      positive: sentimentResult.positiveRatio || sentimentResult.positive / (sentimentResult.total || 1) || 0,
      negative: sentimentResult.negativeRatio || sentimentResult.negative / (sentimentResult.total || 1) || 0,
      neutral: sentimentResult.neutralRatio || sentimentResult.neutral / (sentimentResult.total || 1) || 1
    };
    
  } catch (parseError) {
    console.error(`❌ JSON解析失败:`, parseError.message);
    console.error(`失败时的内容: ${content}`);
    
    // 回退到关键词匹配
    return {
      positive: 0.5,
      negative: 0.3,
      neutral: 0.2
    };
  }
}

testCases.forEach(testCase => {
  const result = parseAIResponse(testCase.content);
  console.log(`📊 解析结果: 积极${(result.positive * 100).toFixed(1)}% 消极${(result.negative * 100).toFixed(1)}% 中性${(result.neutral * 100).toFixed(1)}%`);
});

console.log('\n✅ 测试完成！');
