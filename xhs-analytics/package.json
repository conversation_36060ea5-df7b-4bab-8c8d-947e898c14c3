{"name": "xhs-analytics", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "eslint"}, "dependencies": {"html2canvas": "^1.4.1", "jspdf": "^3.0.2", "lucide-react": "^0.542.0", "next": "15.5.2", "node-fetch": "^3.3.2", "react": "19.1.0", "react-dom": "19.1.0", "react-markdown": "^10.1.0", "recharts": "^3.1.2", "wordcloud": "^1.2.3", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "dotenv": "^17.2.1", "eslint": "^9", "eslint-config-next": "15.5.2", "tailwindcss": "^4", "typescript": "^5"}}