// 专门测试AI API返回内容的脚本
require('dotenv').config({ path: '.env.local' });

const API_KEY = process.env.QWEN_API_KEY;
const API_ENDPOINT = process.env.QWEN_API_ENDPOINT;

async function debugAIResponse() {
  console.log('🔍 调试AI API返回内容');
  console.log('============================');
  
  // 使用和项目中完全相同的提示词和评论数据
  const testComments = [
    "['好香'",
    "'超喜欢牛杂啊啊啊啊！下次一定要试试'",
    "'想去！！！'"
  ];
  
  const sentimentPrompt = `请分析以下小红书评论的情感倾向，按照严格的JSON格式返回结果。

评论内容：
${testComments.join('\n')}

请务必严格按照以下JSON格式返回，不要添加任何解释文字、markdown标记或其他内容：
{
  "positive": 积极评论数量,
  "negative": 消极评论数量,
  "neutral": 中性评论数量,
  "total": 总评论数,
  "positiveRatio": 积极比例(0到1之间的小数),
  "negativeRatio": 消极比例(0到1之间的小数),
  "neutralRatio": 中性比例(0到1之间的小数)
}

分析标准：
- 积极：好、棒、赞、喜欢、推荐、不错、很棒等正面表达
- 消极：差、烂、不好、失望、不推荐、质量差等负面表达
- 中性：一般描述、事实陈述、无明显情感倾向

要求：只返回纯JSON格式，不要添加任何其他文字。`;

  try {
    console.log('📝 发送请求...');
    console.log('API Key:', API_KEY ? '已设置' : '❌ 未设置');
    console.log('API Endpoint:', API_ENDPOINT);
    console.log('');
    
    const response = await fetch(API_ENDPOINT, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${API_KEY}`
      },
      body: JSON.stringify({
        model: 'qwen-turbo',
        input: {
          messages: [{
            role: 'user',
            content: sentimentPrompt
          }]
        },
        parameters: {
          result_format: 'message',
          temperature: 0.1,
          top_p: 0.8,
          max_tokens: 500
        }
      })
    });

    console.log('📤 HTTP响应状态:', response.status, response.statusText);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ API调用失败:', errorText);
      return;
    }

    const result = await response.json();
    console.log('📤 完整API响应:');
    console.log(JSON.stringify(result, null, 2));
    console.log('');

    const content = result.output?.choices?.[0]?.message?.content;
    console.log('🎯 提取的内容:');
    console.log('类型:', typeof content);
    console.log('长度:', content?.length || 0);
    console.log('原始内容:');
    console.log(content);
    console.log('');

    if (!content) {
      console.error('❌ AI返回内容为空');
      return;
    }

    // 测试JSON解析
    console.log('🧪 测试JSON解析:');
    try {
      const parsed = JSON.parse(content);
      console.log('✅ JSON解析成功!');
      console.log('解析结果:', parsed);
    } catch (parseError) {
      console.error('❌ JSON解析失败:', parseError.message);
      console.log('尝试修复...');
      
      // 尝试各种修复方法
      let fixedContent = content.trim();
      
      // 移除markdown代码块
      const jsonMatch = fixedContent.match(/```json\s*([\s\S]*?)\s*```/);
      if (jsonMatch) {
        fixedContent = jsonMatch[1].trim();
        console.log('📝 提取markdown代码块:', fixedContent);
      }
      
      // 移除前后引号
      if (fixedContent.startsWith('"') && fixedContent.endsWith('"')) {
        fixedContent = fixedContent.slice(1, -1);
        console.log('📝 移除前后引号:', fixedContent);
      }
      
      // 再次尝试解析
      try {
        const fixedParsed = JSON.parse(fixedContent);
        console.log('✅ 修复后JSON解析成功!');
        console.log('修复后结果:', fixedParsed);
      } catch (secondError) {
        console.error('❌ 修复后仍然失败:', secondError.message);
        console.log('最终尝试的内容:', fixedContent);
      }
    }

  } catch (error) {
    console.error('❌ 请求过程出错:', error.message);
    console.error('错误详情:', error);
  }
}

// 运行调试
debugAIResponse();