'use client';

import React, { useState, useCallback } from 'react';
import { Upload, FileSpreadsheet, AlertCircle, CheckCircle } from 'lucide-react';
import { XHSPost } from '@/types';
import * as XLSX from 'xlsx';

interface FileUploadProps {
  onDataLoaded: (data: XHSPost[], fileName?: string) => void;
  onError: (error: string) => void;
}

export function FileUpload({ onDataLoaded, onError }: FileUploadProps) {
  const [isDragging, setIsDragging] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [uploadStatus, setUploadStatus] = useState<'idle' | 'success' | 'error'>('idle');

  const processFile = useCallback(async (file: File) => {
    if (!file.name.endsWith('.xlsx') && !file.name.endsWith('.xls')) {
      onError('请上传Excel文件(.xlsx或.xls格式)');
      setUploadStatus('error');
      return;
    }

    setIsProcessing(true);
    setUploadStatus('idle');

    try {
      const arrayBuffer = await file.arrayBuffer();
      const workbook = XLSX.read(arrayBuffer, { type: 'array' });
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      const rawData = XLSX.utils.sheet_to_json(worksheet);

      // 解析中文数字格式（如"2.4万"转换为24000）
      const parseChineseNumber = (value: string | number): number => {
        if (typeof value === 'number') return value;
        if (!value || value === '') return 0;

        const str = value.toString().trim();

        // 如果包含"万"
        if (str.includes('万')) {
          const numStr = str.replace('万', '');
          const num = parseFloat(numStr);
          return Math.round(num * 10000);
        }

        // 如果包含"千"
        if (str.includes('千')) {
          const numStr = str.replace('千', '');
          const num = parseFloat(numStr);
          return Math.round(num * 1000);
        }

        // 普通数字
        return parseInt(str) || 0;
      };

      const processedData: XHSPost[] = (rawData as Record<string, unknown>[]).map((row) => {
        const post: XHSPost = {
          笔记链接: (row['笔记链接'] as string) || '',
          软文标题: (row['软文标题'] as string) || '',
          软文作者: (row['软文作者'] as string) || '',
          软文内容: (row['软文内容'] as string) || '',
          发文日期: (row['发文日期'] as string) || '',
          发文地点: (row['发文地点'] as string) || '',
          点赞数: parseChineseNumber(row['点赞数']),
          收藏数: parseChineseNumber(row['收藏数']),
          评论数: parseChineseNumber(row['评论数']),
          评论: (row['评论'] as string) ? (row['评论'] as string).split(',').map((c: string) => c.trim()) : [],
          软文标签: (row['软文标签'] as string) ? (row['软文标签'] as string).split(',').map((t: string) => t.trim()) : [],
          小红书号: (row['小红书号'] as string) || '',
          IP属地: (row['IP属地'] as string) || '',
          关注: parseChineseNumber(row['关注']),
          粉丝: parseChineseNumber(row['粉丝']),
          获赞与收藏: parseChineseNumber(row['获赞与收藏'])
        };

        return post;
      });

      if (processedData.length === 0) {
        throw new Error('Excel文件中没有找到有效数据');
      }

      onDataLoaded(processedData, file.name);
      setUploadStatus('success');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '文件处理失败';
      onError(errorMessage);
      setUploadStatus('error');
    } finally {
      setIsProcessing(false);
    }
  }, [onDataLoaded, onError]);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      processFile(files[0]);
    }
  }, [processFile]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  }, []);

  const handleFileSelect = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      processFile(files[0]);
    }
  }, [processFile]);

  return (
    <div className="w-full max-w-4xl mx-auto p-6">
      <div
        className={`relative border-2 border-dashed rounded-lg p-8 text-center transition-all duration-200 ${
          isDragging
            ? 'border-blue-500 bg-blue-50'
            : uploadStatus === 'success'
            ? 'border-green-500 bg-green-50'
            : uploadStatus === 'error'
            ? 'border-red-500 bg-red-50'
            : 'border-gray-300 hover:border-gray-400'
        }`}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
      >
        {isProcessing ? (
          <div className="flex flex-col items-center space-y-4">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p className="text-gray-600">正在处理文件...</p>
          </div>
        ) : uploadStatus === 'success' ? (
          <div className="flex flex-col items-center space-y-4">
            <CheckCircle className="h-12 w-12 text-green-500" />
            <p className="text-green-600 font-medium">文件上传成功！</p>
            <p className="text-gray-600 text-sm">数据已加载，开始分析</p>
          </div>
        ) : uploadStatus === 'error' ? (
          <div className="flex flex-col items-center space-y-4">
            <AlertCircle className="h-12 w-12 text-red-500" />
            <p className="text-red-600 font-medium">文件上传失败</p>
            <p className="text-gray-600 text-sm">请检查文件格式并重试</p>
          </div>
        ) : (
          <div className="flex flex-col items-center space-y-4">
            <FileSpreadsheet className="h-12 w-12 text-gray-400" />
            <div>
              <p className="text-lg font-medium text-gray-700">上传Excel文件</p>
              <p className="text-gray-500 text-sm mt-1">
                拖拽文件到此处或点击选择文件
              </p>
            </div>
            <div className="flex items-center space-x-2 text-sm text-gray-500">
              <Upload className="h-4 w-4" />
              <span>支持 .xlsx, .xls 格式</span>
            </div>
          </div>
        )}

        <input
          type="file"
          accept=".xlsx,.xls"
          onChange={handleFileSelect}
          className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
          disabled={isProcessing}
        />
      </div>

      <div className="mt-4 text-sm text-gray-600">
        <p className="font-medium mb-2">文件格式要求：</p>
        <ul className="list-disc list-inside space-y-1 text-xs">
          <li>Excel文件必须包含列：笔记链接、软文标题、软文作者、软文内容、发文日期、发文地点</li>
          <li>数据列：点赞数、收藏数、评论数、评论、软文标签、小红书号、IP属地、关注、粉丝、获赞与收藏</li>
          <li>评论和标签列用逗号分隔多个值</li>
        </ul>
      </div>
    </div>
  );
}