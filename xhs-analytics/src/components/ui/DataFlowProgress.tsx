'use client';

import React from 'react';
import { Loader2, CheckCircle, AlertCircle, Clock } from 'lucide-react';
import { DataFlowState } from '@/types';

interface DataFlowProgressProps {
  state: DataFlowState;
}

export function DataFlowProgress({ state }: DataFlowProgressProps) {
  const getStatusIcon = () => {
    switch (state.status) {
      case 'loading':
      case 'analyzing':
      case 'ai-analyzing':
        return <Loader2 className="h-5 w-5 animate-spin text-blue-500" />;
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'error':
        return <AlertCircle className="h-5 w-5 text-red-500" />;
      default:
        return <Clock className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStatusText = () => {
    switch (state.status) {
      case 'idle':
        return '等待数据上传';
      case 'loading':
        return '正在处理数据...';
      case 'analyzing':
        return '正在进行数据分析...';
      case 'ai-analyzing':
        return state.currentTask || '正在进行AI深度分析...';
      case 'completed':
        return '所有分析完成';
      case 'error':
        return '处理失败';
      default:
        return '准备就绪';
    }
  };

  const getStatusColor = () => {
    switch (state.status) {
      case 'loading':
      case 'analyzing':
      case 'ai-analyzing':
        return 'bg-blue-500';
      case 'completed':
        return 'bg-green-500';
      case 'error':
        return 'bg-red-500';
      default:
        return 'bg-gray-300';
    }
  };

  if (state.status === 'idle') {
    return (
      <div className="w-full max-w-4xl mx-auto p-4">
        <div className="flex items-center space-x-3 text-gray-500">
          <Clock className="h-5 w-5" />
          <span className="text-sm font-medium">等待数据上传</span>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full max-w-4xl mx-auto p-4">
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-3">
            {getStatusIcon()}
            <span className="text-sm font-medium text-gray-700">
              {getStatusText()}
            </span>
          </div>
          <span className="text-sm text-gray-500">{state.progress}%</span>
        </div>

        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className={`h-2 rounded-full transition-all duration-300 ${getStatusColor()}`}
            style={{ width: `${state.progress}%` }}
          />
        </div>

        {state.error && (
          <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-md">
            <div className="flex items-center space-x-2">
              <AlertCircle className="h-4 w-4 text-red-500" />
              <span className="text-sm text-red-700">{state.error}</span>
            </div>
          </div>
        )}

        {state.status === 'completed' && state.results && (
          <div className="mt-3 grid grid-cols-2 md:grid-cols-4 gap-3">
            <div className="text-center p-2 bg-blue-50 rounded">
              <div className="text-lg font-bold text-blue-600">
                {state.results.basic.总笔记数}
              </div>
              <div className="text-xs text-blue-600">总笔记数</div>
            </div>
            <div className="text-center p-2 bg-green-50 rounded">
              <div className="text-lg font-bold text-green-600">
                {state.results.basic.总点赞数.toLocaleString()}
              </div>
              <div className="text-xs text-green-600">总点赞数</div>
            </div>
            <div className="text-center p-2 bg-yellow-50 rounded">
              <div className="text-lg font-bold text-yellow-600">
                {state.results.basic.总收藏数.toLocaleString()}
              </div>
              <div className="text-xs text-yellow-600">总收藏数</div>
            </div>
            <div className="text-center p-2 bg-purple-50 rounded">
              <div className="text-lg font-bold text-purple-600">
                {state.results.basic.总评论数.toLocaleString()}
              </div>
              <div className="text-xs text-purple-600">总评论数</div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}