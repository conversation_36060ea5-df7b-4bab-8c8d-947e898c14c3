'use client';

import React, { useEffect, useState } from 'react';
import { Brain, Users, Target, TrendingUp, BarChart3, Lightbulb, AlertTriangle, CheckCircle } from 'lucide-react';
import ReactMarkdown from 'react-markdown';
import { XHSPost, AIAnalysisResult } from '@/types';

interface AutoAIAnalysisProps {
  posts: XHSPost[];
  autoStart?: boolean;
  precomputedResults?: any[];
}

interface ExtendedAIResult extends AIAnalysisResult {
  analysisType: string;
  title: string;
  icon: React.ComponentType<any>;
  color: string;
}

export default function AutoAIAnalysis({ posts, autoStart = false, precomputedResults = [] }: AutoAIAnalysisProps) {
  const [analysisResults, setAnalysisResults] = useState<ExtendedAIResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [completedCount, setCompletedCount] = useState(0);

  const analysisTypes = [
    {
      id: 'insight',
      title: '深度洞察分析',
      description: '内容表现模式和用户互动趋势深度分析',
      icon: Brain,
      color: 'blue'
    },
    {
      id: 'recommendation',
      title: '创作者推荐分析',
      description: '高价值创作者识别和合作机会分析',
      icon: Users,
      color: 'green'
    },
    {
      id: 'strategy',
      title: '营销策略分析',
      description: '内容营销和投放策略优化建议',
      icon: Target,
      color: 'purple'
    },
    {
      id: 'trend',
      title: '趋势预测分析',
      description: '内容趋势预测和市场机会识别',
      icon: TrendingUp,
      color: 'orange'
    },
    {
      id: 'performance',
      title: '效果评估分析',
      description: '推广效果评估和ROI优化建议',
      icon: BarChart3,
      color: 'indigo'
    },
    {
      id: 'optimization',
      title: '优化建议分析',
      description: '内容优化和用户体验提升建议',
      icon: Lightbulb,
      color: 'yellow'
    }
  ];

  // 使用预计算的结果
  useEffect(() => {
    if (precomputedResults.length > 0) {
      const extendedResults = precomputedResults.map(result => ({
        ...result,
        icon: analysisTypes.find(type => type.id === result.analysisType)?.icon || Brain,
        color: analysisTypes.find(type => type.id === result.analysisType)?.color || 'blue'
      }));
      setAnalysisResults(extendedResults);
      setCompletedCount(extendedResults.length);
    }
  }, [precomputedResults]);

  useEffect(() => {
    if (posts.length > 0 && autoStart && !isLoading && analysisResults.length === 0 && precomputedResults.length === 0) {
      console.log('🚀 自动启动AI深度分析...');
      performAllAnalyses();
    }
  }, [posts, autoStart, isLoading, analysisResults.length, precomputedResults.length]);

  const performAllAnalyses = async () => {
    if (posts.length === 0) return;

    setIsLoading(true);
    setCompletedCount(0);
    setAnalysisResults([]);

    const results: ExtendedAIResult[] = [];

    for (let i = 0; i < analysisTypes.length; i++) {
      const analysisType = analysisTypes[i];
      
      try {
        console.log(`🤖 开始执行 ${analysisType.title}...`);
        
        const response = await fetch('/api/ai-analysis', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            posts: posts.slice(0, 50),
            analysisType: analysisType.id
          })
        });

        if (!response.ok) {
          throw new Error(`${analysisType.title}分析失败`);
        }

        const result: AIAnalysisResult = await response.json();
        
        const extendedResult: ExtendedAIResult = {
          ...result,
          analysisType: analysisType.id,
          title: analysisType.title,
          icon: analysisType.icon,
          color: analysisType.color
        };

        results.push(extendedResult);
        setAnalysisResults([...results]);
        setCompletedCount(i + 1);
        
        console.log(`✅ ${analysisType.title} 完成`);
        
        // 添加延迟避免API限流，使用5秒间隔确保qwen-plus稳定调用
        if (i < analysisTypes.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 5000));
        }
        
      } catch (error) {
        console.error(`❌ ${analysisType.title}失败:`, error);
        
        // 添加失败的占位结果
        const failedResult: ExtendedAIResult = {
          洞察: `${analysisType.title}暂时无法完成，请稍后重试`,
          推荐: ['分析服务暂时不可用'],
          策略: '请稍后重新尝试分析',
          analysisType: analysisType.id,
          title: analysisType.title,
          icon: analysisType.icon,
          color: 'gray'
        };
        
        results.push(failedResult);
        setAnalysisResults([...results]);
        setCompletedCount(i + 1);
      }
    }

    setIsLoading(false);
    console.log('🎉 所有AI分析完成！');
  };

  const getProgressPercentage = () => {
    return Math.round((completedCount / analysisTypes.length) * 100);
  };

  if (posts.length === 0) {
    return (
      <div className="w-full max-w-7xl mx-auto p-6">
        <div className="bg-white p-8 rounded-lg shadow-sm border text-center">
          <AlertTriangle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">暂无数据</h3>
          <p className="text-gray-600">请先上传数据文件以开始AI深度分析</p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full max-w-7xl mx-auto p-6 space-y-6">
      {/* 分析进度 */}
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">AI深度分析</h2>
            <p className="text-gray-600 mt-1">
              {isLoading ? 
                `正在执行第 ${completedCount + 1}/${analysisTypes.length} 项分析...` : 
                `已完成 ${analysisResults.length}/${analysisTypes.length} 项分析`
              }
            </p>
          </div>
          {isLoading && (
            <div className="flex items-center space-x-2">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
              <span className="text-sm text-gray-600">{getProgressPercentage()}%</span>
            </div>
          )}
          {!isLoading && analysisResults.length > 0 && (
            <CheckCircle className="h-6 w-6 text-green-600" />
          )}
        </div>
        
        {/* 进度条 */}
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className="bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full transition-all duration-500"
            style={{ width: `${getProgressPercentage()}%` }}
          />
        </div>
        
        {/* 分析类型状态 */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-2 mt-4">
          {analysisTypes.map((type, index) => {
            const isCompleted = index < completedCount;
            const isCurrent = index === completedCount && isLoading;
            const Icon = type.icon;
            
            return (
              <div
                key={type.id}
                className={`p-2 rounded-lg text-center transition-all ${
                  isCompleted ? 'bg-green-50 border border-green-200' :
                  isCurrent ? 'bg-blue-50 border border-blue-200' :
                  'bg-gray-50 border border-gray-200'
                }`}
              >
                <Icon className={`h-4 w-4 mx-auto mb-1 ${
                  isCompleted ? 'text-green-600' :
                  isCurrent ? 'text-blue-600' :
                  'text-gray-400'
                }`} />
                <div className={`text-xs font-medium ${
                  isCompleted ? 'text-green-700' :
                  isCurrent ? 'text-blue-700' :
                  'text-gray-500'
                }`}>
                  {type.title.replace('分析', '')}
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* 分析结果展示 */}
      {analysisResults.length > 0 && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {analysisResults.map((result, index) => {
            const Icon = result.icon;
            
            return (
              <div key={index} className="bg-white p-6 rounded-lg shadow-sm border">
                <div className="flex items-center space-x-3 mb-4">
                  <div className={`p-2 rounded-lg bg-${result.color}-100`}>
                    <Icon className={`h-6 w-6 text-${result.color}-600`} />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">{result.title}</h3>
                  </div>
                </div>

                <div className="space-y-4">
                  {/* 洞察 */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">💡 核心洞察</h4>
                    <div className="text-gray-700 text-sm leading-relaxed prose prose-sm max-w-none">
                      <ReactMarkdown>
                        {typeof result.洞察 === 'string' ? result.洞察 : JSON.stringify(result.洞察, null, 2)}
                      </ReactMarkdown>
                    </div>
                  </div>

                  {/* 推荐建议 */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">📋 具体建议</h4>
                    <ul className="space-y-2">
                      {Array.isArray(result.推荐) ? result.推荐.slice(0, 3).map((recommendation, idx) => (
                        <li key={idx} className="flex items-start space-x-2 text-sm">
                          <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                          <div className="text-gray-700 prose prose-sm max-w-none">
                            <ReactMarkdown>
                              {typeof recommendation === 'string' ? recommendation : JSON.stringify(recommendation, null, 2)}
                            </ReactMarkdown>
                          </div>
                        </li>
                      )) : (
                        <li className="text-gray-500 text-sm">暂无推荐建议</li>
                      )}
                    </ul>
                  </div>

                  {/* 策略建议 */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">🎯 策略建议</h4>
                    <div className="text-gray-700 text-sm leading-relaxed">
                      {typeof result.策略 === 'string' ? (
                        <p>{result.策略}</p>
                      ) : typeof result.策略 === 'object' && result.策略 ? (
                        <div className="space-y-2">
                          {Object.entries(result.策略).map(([key, value]) => (
                            <div key={key}>
                              <strong>{key}:</strong> {String(value)}
                            </div>
                          ))}
                        </div>
                      ) : (
                        <p>策略分析中...</p>
                      )}
                    </div>
                  </div>


                </div>
              </div>
            );
          })}
        </div>
      )}

      {/* 重新分析按钮 */}
      {!isLoading && (
        <div className="text-center">
          <button
            onClick={performAllAnalyses}
            className="px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200 font-medium"
          >
            🔄 重新执行AI分析
          </button>
        </div>
      )}
    </div>
  );
}
