'use client';

import React, { useState } from 'react';
import { Brain, TrendingUp, Users, Target, Lightbulb, Loader2 } from 'lucide-react';
import { XHSPost, AIAnalysisResult } from '@/types';

interface OnDemandAIProps {
  posts: XHSPost[];
  onAnalysisComplete: (result: AIAnalysisResult) => void;
}

export function OnDemandAI({ posts, onAnalysisComplete }: OnDemandAIProps) {
  const [selectedAnalysis, setSelectedAnalysis] = useState<'insight' | 'recommendation' | 'strategy'>('insight');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [result, setResult] = useState<AIAnalysisResult | null>(null);

  const analysisTypes = [
    {
      id: 'insight' as const,
      title: '深度洞察',
      description: '分析内容表现模式和用户互动趋势',
      icon: Brain,
      color: 'blue'
    },
    {
      id: 'recommendation' as const,
      title: '创作者推荐',
      description: '识别高价值创作者和合作机会',
      icon: Users,
      color: 'green'
    },
    {
      id: 'strategy' as const,
      title: '策略建议',
      description: '提供内容营销和投放策略',
      icon: Target,
      color: 'purple'
    }
  ];

  const handleAnalyze = async () => {
    if (posts.length === 0) {
      alert('请先上传数据');
      return;
    }

    setIsAnalyzing(true);
    try {
      const response = await fetch('/api/ai-analysis', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          posts: posts.slice(0, 50),
          analysisType: selectedAnalysis
        })
      });

      if (!response.ok) {
        throw new Error('AI分析失败');
      }

      const analysisResult: AIAnalysisResult = await response.json();
      setResult(analysisResult);
      onAnalysisComplete(analysisResult);
    } catch (error) {
      console.error('AI analysis error:', error);
      alert('AI分析失败，请重试');
    } finally {
      setIsAnalyzing(false);
    }
  };

  const getSentimentColor = (sentiment: number) => {
    if (sentiment > 0.6) return 'text-green-600';
    if (sentiment > 0.4) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <div className="w-full max-w-7xl mx-auto p-6 space-y-6">
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-gray-900">AI深度分析</h2>
          <div className="text-xs text-gray-500 bg-blue-50 p-2 rounded max-w-md">
            <strong>说明：</strong>AI对整批数据进行智能分析，提供商业价值评估、用户情感分析和营销策略建议。帮助您从整体角度了解数据价值。
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          {analysisTypes.map((type) => {
            const Icon = type.icon;
            const isSelected = selectedAnalysis === type.id;
            
            return (
              <button
                key={type.id}
                onClick={() => setSelectedAnalysis(type.id)}
                className={`p-4 rounded-lg border-2 text-left transition-all ${
                  isSelected
                    ? `border-${type.color}-500 bg-${type.color}-50`
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center space-x-3">
                  <Icon className={`h-6 w-6 ${isSelected ? `text-${type.color}-600` : 'text-gray-400'}`} />
                  <div>
                    <h3 className="font-medium text-gray-900">{type.title}</h3>
                    <p className="text-sm text-gray-600 mt-1">{type.description}</p>
                  </div>
                </div>
              </button>
            );
          })}
        </div>

        <button
          onClick={handleAnalyze}
          disabled={isAnalyzing}
          className="w-full md:w-auto px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
        >
          {isAnalyzing ? (
            <>
              <Loader2 className="h-4 w-4 animate-spin" />
              <span>正在分析...</span>
            </>
          ) : (
            <>
              <Lightbulb className="h-4 w-4" />
              <span>开始AI分析</span>
            </>
          )}
        </button>
      </div>

      {result && (
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">分析结果</h3>
          
          <div className="space-y-6">
            <div>
              <h4 className="font-medium text-gray-900 mb-2 flex items-center">
                <Brain className="h-4 w-4 mr-2 text-blue-600" />
                深度洞察
              </h4>
              <p className="text-gray-700 leading-relaxed">{result.洞察}</p>
              <div className="text-xs text-gray-500 bg-blue-50 p-2 rounded mt-2">
                <strong>说明：</strong>AI对内容表现和用户互动模式的深度洞察，帮助理解数据背后的趋势和规律。
              </div>
            </div>

            <div>
              <h4 className="font-medium text-gray-900 mb-2 flex items-center">
                <TrendingUp className="h-4 w-4 mr-2 text-green-600" />
                推荐建议
              </h4>
              <ul className="space-y-2">
                {result.推荐.map((recommendation, index) => (
                  <li key={index} className="flex items-start space-x-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                    <span className="text-gray-700">{recommendation}</span>
                  </li>
                ))}
              </ul>
              <div className="text-xs text-gray-500 bg-green-50 p-2 rounded mt-2">
                <strong>说明：</strong>AI基于数据分析给出的具体建议，包括优化方向、合作机会、重点关注等内容。
              </div>
            </div>

            <div>
                <h4 className="font-medium text-gray-900 mb-2 flex items-center">
                  <Target className="h-4 w-4 mr-2 text-purple-600" />
                  策略建议
                </h4>
                <p className="text-gray-700 leading-relaxed">{result.策略}</p>
                <div className="text-xs text-gray-500 bg-purple-50 p-2 rounded mt-2">
                  <strong>说明：</strong>基于AI分析结果提供的整体营销策略建议，包括内容优化、合作方式、投放时机等。
                </div>
              </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium text-gray-900 mb-3">商业价值评估</h4>
                <div className="flex items-center space-x-3">
                  <div className="flex-1 bg-gray-200 rounded-full h-3">
                    <div
                      className="bg-gradient-to-r from-red-500 via-yellow-500 to-green-500 h-3 rounded-full transition-all duration-500"
                      style={{ width: `${result.商业价值评估}%` }}
                    />
                  </div>
                  <span className="text-lg font-bold text-gray-900">{result.商业价值评估}</span>
                </div>
                <p className="text-sm text-gray-600 mt-1">
                  {result.商业价值评估 >= 80 ? '极高商业价值' : 
                   result.商业价值评估 >= 60 ? '较高商业价值' : 
                   result.商业价值评估 >= 40 ? '中等商业价值' : '较低商业价值'}
                </p>
                <div className="text-xs text-gray-500 bg-orange-50 p-2 rounded mt-2">
                  <strong>说明：</strong>AI对整批数据的综合商业价值评分（0-100分），考虑内容质量、用户反馈、商业潜力等因素。分数越高表示整体商业价值越大。
                </div>
              </div>

              <div>
                <h4 className="font-medium text-gray-900 mb-3">情感分析</h4>
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">积极</span>
                    <span className={`text-sm font-medium ${getSentimentColor(result.情感分析.积极)}`}>
                      {(result.情感分析.积极 * 100).toFixed(1)}%
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">中性</span>
                    <span className="text-sm font-medium text-gray-600">
                      {(result.情感分析.中性 * 100).toFixed(1)}%
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">消极</span>
                    <span className={`text-sm font-medium ${getSentimentColor(result.情感分析.消极)}`}>
                      {(result.情感分析.消极 * 100).toFixed(1)}%
                    </span>
                  </div>
                </div>
                <div className="text-xs text-gray-500 bg-green-50 p-2 rounded mt-2">
                  <strong>说明：</strong>AI对整批评论的情感分析，积极比例高表示用户反馈正面，消极比例高表示存在负面评价。帮助评估整体口碑情况。
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}