'use client';

import React, { useState } from 'react';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell, LineChart, Line } from 'recharts';
import { TrendingUp, Users, Heart, Bookmark, MessageSquare, Star, MessageCircle } from 'lucide-react';
import { AnalyticsResult, AuthorAnalysis, ContentCategory, XHSPost } from '@/types';
import CommentWordCloud from './CommentWordCloud';

interface DashboardProps {
  data: {
    basic: AnalyticsResult;
    authors: AuthorAnalysis[];
    categories: ContentCategory[];
  };
  posts: XHSPost[];
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D'];

export function Dashboard({ data, posts }: DashboardProps) {
  const { basic, authors, categories } = data;
  const [selectedSentiment, setSelectedSentiment] = useState<'positive' | 'negative' | 'neutral' | 'all'>('all');

  // 显示所有创作者，不再限制为前10个
  const topAuthors = authors;
  const categoryData = categories.map(cat => ({
    name: cat.分类,
    value: cat.笔记数,
    avgEngagement: cat.平均互动数
  }));

  // 为图表显示取前10个，但表格显示全部
  const engagementData = authors.slice(0, 10).map(author => ({
    name: author.作者.length > 8 ? author.作者.substring(0, 8) + '...' : author.作者,
    likes: author.总点赞数,
    favorites: author.总收藏数,
    comments: author.总评论数,
    engagement: author.积极情感度
  }));

  return (
    <div className="w-full max-w-7xl mx-auto p-6 space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="flex items-center justify-between mb-2">
            <div>
              <p className="text-sm font-medium text-gray-600">总笔记数</p>
              <p className="text-2xl font-bold text-gray-900">{basic.总笔记数}</p>
            </div>
            <MessageSquare className="h-8 w-8 text-blue-500" />
          </div>
          <div className="text-xs text-gray-500 bg-blue-50 p-2 rounded">
            <strong>💡 如何使用：</strong>数值越高说明数据规模越大，分析结果越可靠。建议总笔记数大于100时进行重要决策。
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="flex items-center justify-between mb-2">
            <div>
              <p className="text-sm font-medium text-gray-600">总点赞数</p>
              <p className="text-2xl font-bold text-gray-900">{basic.总点赞数.toLocaleString()}</p>
            </div>
            <Heart className="h-8 w-8 text-red-500" />
          </div>
          <div className="text-xs text-gray-500 bg-red-50 p-2 rounded">
            <strong>💡 如何使用：</strong>反映内容整体受欢迎程度。点赞数大于收藏数说明内容偏向娱乐性，收藏数大于点赞数说明内容实用性更强。
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="flex items-center justify-between mb-2">
            <div>
              <p className="text-sm font-medium text-gray-600">总收藏数</p>
              <p className="text-2xl font-bold text-gray-900">{basic.总收藏数.toLocaleString()}</p>
            </div>
            <Bookmark className="h-8 w-8 text-yellow-500" />
          </div>
          <div className="text-xs text-gray-500 bg-yellow-50 p-2 rounded">
            <strong>💡 如何使用：</strong>收藏数高说明内容实用性强，用户愿意保存查看。这类内容适合做产品推荐和教程分享。
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="flex items-center justify-between mb-2">
            <div>
              <p className="text-sm font-medium text-gray-600">总评论数</p>
              <p className="text-2xl font-bold text-gray-900">{basic.总评论数.toLocaleString()}</p>
            </div>
            <MessageSquare className="h-8 w-8 text-green-500" />
          </div>
          <div className="text-xs text-gray-500 bg-green-50 p-2 rounded">
            <strong>💡 如何使用：</strong>评论数高说明内容互动性强，能引发用户讨论。适合做话题性内容和产品测评。
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">内容分类分布</h3>
            <div className="text-xs text-gray-500 bg-green-50 p-2 rounded max-w-md">
              <strong>说明：</strong>根据笔记标题和内容自动分类，帮助了解内容类型分布。教程类内容实用性高，体验类内容互动性强。
            </div>
          </div>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={categoryData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }: { name?: string; percent?: number }) => `${name || ''} ${((percent || 0) * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {categoryData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">热门创作者互动数据</h3>
            <div className="text-xs text-gray-500 bg-purple-50 p-2 rounded max-w-md">
              <strong>说明：</strong>展示排名前5创作者的互动数据对比，帮助识别哪些创作者的粉丝更活跃，内容更受欢迎。
            </div>
          </div>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={engagementData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="engagement" fill="#8884d8" name="平均互动数" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>

      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">创作者表现排行</h3>
          <div className="text-xs text-gray-500 bg-blue-50 p-2 rounded max-w-md">
            <strong>📊 商业价值计算公式：</strong>
            <div className="mt-1 font-mono text-xs">
              ROI效率 × 35% + 互动率 × 25% + 平均互动 × 15% + 内容质量 × 15% + 粉丝规模 × 5% + 情感分析 × 5%
            </div>
            <div className="mt-2">
              <strong>✨ AI情感分析：</strong>积极情感度现在使用通义千问AI进行智能分析，能准确识别网络用语、反讽和复杂情感，准确度大幅提升！
            </div>
          </div>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创作者</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">笔记数</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">总点赞</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">总收藏</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">总评论</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">积极情感度</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">商业价值</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {topAuthors.map((author, index) => (
                <tr key={author.作者} className={index < 3 ? 'bg-blue-50' : ''}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      {index < 3 && <Star className="h-4 w-4 text-yellow-500 mr-2" />}
                      <span className="text-sm font-medium text-gray-900">{author.作者}</span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{author.笔记数}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{author.总点赞数.toLocaleString()}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{author.总收藏数.toLocaleString()}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{author.总评论数.toLocaleString()}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{author.积极情感度.toLocaleString()}%</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-2 w-16">
                        <div
                          className="h-2 bg-gradient-to-r from-red-500 via-yellow-500 to-green-500 rounded-full"
                          style={{
                            background: `linear-gradient(to right, #ef4444 0%, #eab308 50%, #22c55e 100%)`,
                            mask: `linear-gradient(to right, black ${author.商业价值评分}%, transparent ${author.商业价值评分}%)`
                          }}
                        />
                      </div>
                      <span className="ml-2 text-sm font-medium text-gray-900">{author.商业价值评分}</span>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">最高点赞笔记</h3>
          <p className="text-sm font-medium text-gray-900 mb-1">{basic.最高点赞笔记.软文标题}</p>
          <p className="text-sm text-gray-600 mb-1">作者：{basic.最高点赞笔记.软文作者}</p>
          <p className="text-sm text-red-600 font-medium">{basic.最高点赞笔记.点赞数.toLocaleString()} 赞</p>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">最高收藏笔记</h3>
          <p className="text-sm font-medium text-gray-900 mb-1">{basic.最高收藏笔记.软文标题}</p>
          <p className="text-sm text-gray-600 mb-1">作者：{basic.最高收藏笔记.软文作者}</p>
          <p className="text-sm text-yellow-600 font-medium">{basic.最高收藏笔记.收藏数.toLocaleString()} 收藏</p>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">最高评论笔记</h3>
          <p className="text-sm font-medium text-gray-900 mb-1">{basic.最高评论笔记.软文标题}</p>
          <p className="text-sm text-gray-600 mb-1">作者：{basic.最高评论笔记.软文作者}</p>
          <p className="text-sm text-green-600 font-medium">{basic.最高评论笔记.评论数.toLocaleString()} 评论</p>
        </div>
      </div>

      {/* 评论词云分析模块 */}
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <MessageCircle className="h-6 w-6 text-purple-500" />
            <h3 className="text-lg font-semibold text-gray-900">评论词云分析</h3>
          </div>
          <div className="text-xs text-gray-500 bg-purple-50 p-2 rounded max-w-md">
            <strong>说明：</strong>基于AI情感分析的评论关键词提取，帮助了解用户真实反馈和情感倾向。词汇大小表示出现频率，颜色表示情感分类。
          </div>
        </div>

        {/* 情感类型选择器 */}
        <div className="mb-6">
          <div className="flex flex-wrap gap-2">
            {[
              { key: 'all', label: '全部评论', color: 'blue' },
              { key: 'positive', label: '积极评论', color: 'green' },
              { key: 'negative', label: '消极评论', color: 'red' },
              { key: 'neutral', label: '中性评论', color: 'gray' }
            ].map((option) => (
              <button
                key={option.key}
                onClick={() => setSelectedSentiment(option.key as any)}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-all ${
                  selectedSentiment === option.key
                    ? `bg-${option.color}-100 text-${option.color}-700 border-2 border-${option.color}-300`
                    : 'bg-gray-100 text-gray-600 border-2 border-transparent hover:bg-gray-200'
                }`}
              >
                {option.label}
              </button>
            ))}
          </div>
        </div>

        {/* 词云展示区域 */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="space-y-4">
            <CommentWordCloud
              posts={posts}
              sentimentType={selectedSentiment}
            />
          </div>

          {/* 统计信息 */}
          <div className="space-y-4">
            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="text-md font-medium text-gray-900 mb-3">评论统计概览</h4>
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">总评论数</span>
                  <span className="text-sm font-medium text-gray-900">
                    {posts.reduce((sum, post) => sum + (post.评论?.length || 0), 0).toLocaleString()}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">有评论的笔记</span>
                  <span className="text-sm font-medium text-gray-900">
                    {posts.filter(post => post.评论?.length > 0).length}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">平均每篇评论数</span>
                  <span className="text-sm font-medium text-gray-900">
                    {posts.length > 0 ?
                      (posts.reduce((sum, post) => sum + (post.评论?.length || 0), 0) / posts.length).toFixed(1)
                      : '0'
                    }
                  </span>
                </div>
              </div>
            </div>

            <div className="bg-gradient-to-r from-purple-50 to-pink-50 p-4 rounded-lg border border-purple-200">
              <h4 className="text-md font-medium text-purple-900 mb-2">💡 词云分析洞察</h4>
              <div className="text-sm text-purple-700 space-y-1">
                <p>• <strong>积极评论</strong>：关注用户喜爱的产品特点和体验</p>
                <p>• <strong>消极评论</strong>：识别产品问题和改进机会</p>
                <p>• <strong>中性评论</strong>：了解用户关注的功能和使用场景</p>
                <p>• <strong>高频词汇</strong>：代表用户最关心的核心要素</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}