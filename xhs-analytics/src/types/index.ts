export interface XHSPost {
  笔记链接: string;
  软文标题: string;
  软文作者: string;
  软文内容: string;
  发文日期: string;
  发文地点: string;
  点赞数: number;
  收藏数: number;
  评论数: number;
  评论: string[];
  软文标签: string[];
  小红书号: string;
  IP属地: string;
  关注: number;
  粉丝: number;
  获赞与收藏: number;
}

export interface DerivedMetrics {
  互动数: number;
  互动率: number;
  收藏率: number;
  商业价值评分: number;
}

export interface AnalyticsResult {
  总笔记数: number;
  总点赞数: number;
  总收藏数: number;
  总评论数: number;
  平均点赞数: number;
  平均收藏数: number;
  平均评论数: number;
  最高点赞笔记: XHSPost;
  最高收藏笔记: XHSPost;
  最高评论笔记: XHSPost;
}

export interface AuthorAnalysis {
  作者: string;
  笔记数: number;
  总点赞数: number;
  总收藏数: number;
  总评论数: number;
  积极情感度: number; // 替换原来的平均互动数
  粉丝数: number;
  商业价值评分: number;
}

export interface ContentCategory {
  分类: string;
  笔记数: number;
  总点赞数: number;
  总收藏数: number;
  总评论数: number;
  平均互动数: number;
}

export interface AIAnalysisResult {
  洞察: string;
  推荐: string[];
  策略: string;
}

export interface DataFlowState {
  status: 'idle' | 'loading' | 'analyzing' | 'ai-analyzing' | 'completed' | 'error';
  progress: number;
  error?: string;
  currentTask?: string;
  results?: {
    basic: AnalyticsResult;
    authors: AuthorAnalysis[];
    categories: ContentCategory[];
    aiAnalysis?: any[];
  };
}