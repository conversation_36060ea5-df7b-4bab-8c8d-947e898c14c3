'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { FileUpload } from '@/components/ui/FileUpload';
import { DataFlowProgress } from '@/components/ui/DataFlowProgress';

import { Dashboard } from '@/components/analytics/Dashboard';
import AutoAIAnalysis from '@/components/analytics/AutoAIAnalysis';
import { XHSPost, DataFlowState, AIAnalysisResult } from '@/types';
import { DataFlowManager } from '@/services/dataFlow';

export default function Home() {
  const [posts, setPosts] = useState<XHSPost[]>([]);
  const [dataFlowState, setDataFlowState] = useState<DataFlowState>({
    status: 'idle',
    progress: 0
  });
  const [error, setError] = useState<string>('');
  const [dataFlowManager] = useState(() => new DataFlowManager());
  const [fileName, setFileName] = useState<string>('');

  useEffect(() => {
    const unsubscribe = dataFlowManager.subscribe(setDataFlowState);
    return unsubscribe;
  }, [dataFlowManager]);



  const handleDataLoaded = async (data: XHSPost[], uploadedFileName?: string) => {
    try {
      setPosts(data);
      setError('');
      if (uploadedFileName) {
        setFileName(uploadedFileName);
      }
      await dataFlowManager.processData(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : '数据处理失败');
    }
  };

  const handleError = (errorMessage: string) => {
    setError(errorMessage);
  };

  const handleAIAnalysisComplete = (result: AIAnalysisResult) => {
    console.log('AI分析完成:', result);
  };

  const hasData = posts.length > 0 && dataFlowState.status === 'completed';

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">小红书UGC内容营销分析系统</h1>
              <p className="text-gray-600 mt-1">基于AI技术的深度内容分析和营销策略建议</p>
            </div>

          </div>
        </div>
      </div>

      <div className="py-8">
        <FileUpload 
          onDataLoaded={handleDataLoaded}
          onError={handleError}
        />

        {error && (
          <div className="w-full max-w-4xl mx-auto p-4">
            <div className="bg-red-50 border border-red-200 rounded-md p-4">
              <p className="text-red-700">{error}</p>
            </div>
          </div>
        )}

        <DataFlowProgress state={dataFlowState} />

        {hasData && (
          <div className="w-full">
            {/* 电梯导航 */}
            <div className="sticky top-0 z-50 bg-white/95 backdrop-blur-sm border-b border-gray-200 mb-8 no-print">
              <div className="max-w-7xl mx-auto px-6 py-4">
                <nav className="flex justify-center space-x-8">
                  <button
                    onClick={() => document.getElementById('dashboard-section')?.scrollIntoView({ behavior: 'smooth' })}
                    className="px-4 py-2 text-sm font-medium text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-md transition-colors"
                  >
                    📊 数据仪表板
                  </button>
                  <button
                    onClick={() => document.getElementById('ai-analysis-section')?.scrollIntoView({ behavior: 'smooth' })}
                    className="px-4 py-2 text-sm font-medium text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-md transition-colors"
                  >
                    🤖 AI 深度分析
                  </button>

                </nav>
              </div>
            </div>

            {/* 数据仪表板区域 */}
            <section id="dashboard-section" className="mb-16">
              <div className="max-w-7xl mx-auto px-6">
                <div className="bg-white rounded-lg shadow-sm border p-6">
                  <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                    📊 数据仪表板
                  </h2>
                  {dataFlowState.results && (
                    <Dashboard data={dataFlowState.results} posts={posts} />
                  )}
                </div>
              </div>
            </section>

            {/* AI 深度分析区域 */}
            <section id="ai-analysis-section" className="mb-16">
              <div className="max-w-7xl mx-auto px-6">
                <div className="bg-white rounded-lg shadow-sm border p-6">
                  <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                    🤖 AI 深度分析
                  </h2>
                  <AutoAIAnalysis
                    posts={posts}
                    autoStart={false}
                    precomputedResults={dataFlowState.results?.aiAnalysis || []}
                  />
                </div>
              </div>
            </section>
          </div>
        )}
      </div>
    </div>
  );
}