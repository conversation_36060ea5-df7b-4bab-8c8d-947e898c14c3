import { NextRequest, NextResponse } from 'next/server';
import { AIService } from '@/services/ai';

export async function POST(request: NextRequest) {
  try {
    const { posts, analysisType } = await request.json();

    if (!posts || !Array.isArray(posts) || posts.length === 0) {
      return NextResponse.json(
        { error: 'Invalid or empty posts data' },
        { status: 400 }
      );
    }

    if (!['insight', 'recommendation', 'strategy', 'trend', 'performance', 'optimization'].includes(analysisType)) {
      return NextResponse.json(
        { error: 'Invalid analysis type' },
        { status: 400 }
      );
    }

    const aiService = new AIService();
    const result = await aiService.analyzeContent(posts, analysisType);

    return NextResponse.json(result);
  } catch (error) {
    console.error('AI analysis API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}