import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { comments } = await request.json();

    if (!comments || !Array.isArray(comments) || comments.length === 0) {
      return NextResponse.json(
        { error: 'Invalid or empty comments data' },
        { status: 400 }
      );
    }

    // 获取环境变量
    const apiKey = process.env.QWEN_API_KEY;
    const apiEndpoint = process.env.QWEN_API_ENDPOINT;
    const modelName = 'qwen-turbo';

    if (!apiKey || !apiEndpoint) {
      console.error('❌ API配置缺失');
      return NextResponse.json(
        { error: 'API configuration missing' },
        { status: 500 }
      );
    }

    // 清理评论数据
    const cleanedComments = comments
      .filter(comment => comment && typeof comment === 'string' && comment.trim().length > 0)
      .map(comment => comment.trim());

    if (cleanedComments.length === 0) {
      return NextResponse.json({
        positive: 0,
        negative: 0,
        neutral: 1
      });
    }

    console.log(`📊 处理评论总数: ${cleanedComments.length}`);

    // 智能处理策略：根据评论数量选择处理方式
    if (cleanedComments.length <= 50) {
      // 少量评论：直接AI分析
      return await processCommentsWithAI(cleanedComments, apiKey, apiEndpoint, modelName);
    } else if (cleanedComments.length <= 200) {
      // 中等数量：分批AI分析
      return await processBatchCommentsWithAI(cleanedComments, apiKey, apiEndpoint, modelName);
    } else {
      // 大量评论：智能采样+AI分析
      return await processLargeCommentsWithSampling(cleanedComments, apiKey, apiEndpoint, modelName);
    }


  } catch (error) {
    console.error('服务端情感分析失败:', error);

    // 回退到关键词匹配
    const { comments } = await request.json();
    const cleanedComments = comments
      .filter((comment: string) => comment && typeof comment === 'string' && comment.trim().length > 0)
      .map((comment: string) => comment.trim());

    return NextResponse.json(
      await fallbackSentimentAnalysis(cleanedComments)
    );
  }
}

// 处理少量评论：直接AI分析
async function processCommentsWithAI(
  comments: string[],
  apiKey: string,
  apiEndpoint: string,
  modelName: string
): Promise<NextResponse> {
  console.log(`📝 直接AI分析 - 评论数量: ${comments.length}`);

  const sentimentPrompt = `请分析以下小红书评论的情感倾向，按照严格的JSON格式返回结果。

评论内容：
${comments.join('\n')}

请务必严格按照以下JSON格式返回，不要添加任何解释文字、markdown标记或其他内容：
{
  "positive": 积极评论数量,
  "negative": 消极评论数量,
  "neutral": 中性评论数量,
  "total": 总评论数,
  "positiveRatio": 积极比例(0到1之间的小数),
  "negativeRatio": 消极比例(0到1之间的小数),
  "neutralRatio": 中性比例(0到1之间的小数)
}

分析标准：
- 积极：好、棒、赞、喜欢、推荐、不错、很棒等正面表达
- 消极：差、烂、不好、失望、不推荐、质量差等负面表达
- 中性：一般描述、事实陈述、无明显情感倾向

要求：只返回纯JSON格式，不要添加任何其他文字。`;

  return await callAIAPI(sentimentPrompt, apiKey, apiEndpoint, modelName, comments);
}

// 处理中等数量评论：分批AI分析
async function processBatchCommentsWithAI(
  comments: string[],
  apiKey: string,
  apiEndpoint: string,
  modelName: string
): Promise<NextResponse> {
  console.log(`📝 分批AI分析 - 评论数量: ${comments.length}`);

  const batchSize = 40; // 每批处理40条评论
  const batches = [];

  for (let i = 0; i < comments.length; i += batchSize) {
    batches.push(comments.slice(i, i + batchSize));
  }

  console.log(`📝 分为 ${batches.length} 批处理`);

  let totalPositive = 0, totalNegative = 0, totalNeutral = 0, totalCount = 0;

  for (let i = 0; i < batches.length; i++) {
    const batch = batches[i];
    console.log(`📝 处理第 ${i + 1}/${batches.length} 批，${batch.length} 条评论`);

    try {
      const sentimentPrompt = `请分析以下小红书评论的情感倾向，按照严格的JSON格式返回结果。

评论内容：
${batch.join('\n')}

请务必严格按照以下JSON格式返回，不要添加任何解释文字、markdown标记或其他内容：
{
  "positive": 积极评论数量,
  "negative": 消极评论数量,
  "neutral": 中性评论数量,
  "total": 总评论数,
  "positiveRatio": 积极比例(0到1之间的小数),
  "negativeRatio": 消极比例(0到1之间的小数),
  "neutralRatio": 中性比例(0到1之间的小数)
}

分析标准：
- 积极：好、棒、赞、喜欢、推荐、不错、很棒等正面表达
- 消极：差、烂、不好、失望、不推荐、质量差等负面表达
- 中性：一般描述、事实陈述、无明显情感倾向

要求：只返回纯JSON格式，不要添加任何其他文字。`;

      const result = await callAIAPIForBatch(sentimentPrompt, apiKey, apiEndpoint, modelName);

      if (result) {
        totalPositive += result.positive || 0;
        totalNegative += result.negative || 0;
        totalNeutral += result.neutral || 0;
        totalCount += result.total || batch.length;
      } else {
        // 如果AI分析失败，使用关键词匹配作为回退
        const fallbackResult = await fallbackSentimentAnalysis(batch);
        totalPositive += fallbackResult.positive * batch.length;
        totalNegative += fallbackResult.negative * batch.length;
        totalNeutral += fallbackResult.neutral * batch.length;
        totalCount += batch.length;
      }
    } catch (error) {
      console.error(`❌ 第 ${i + 1} 批处理失败:`, error);
      // 使用关键词匹配作为回退
      const fallbackResult = await fallbackSentimentAnalysis(batch);
      totalPositive += fallbackResult.positive * batch.length;
      totalNegative += fallbackResult.negative * batch.length;
      totalNeutral += fallbackResult.neutral * batch.length;
      totalCount += batch.length;
    }
  }

  // 计算最终比例
  const finalPositive = totalCount > 0 ? totalPositive / totalCount : 0;
  const finalNegative = totalCount > 0 ? totalNegative / totalCount : 0;
  const finalNeutral = totalCount > 0 ? totalNeutral / totalCount : 1;

  console.log(`✅ 分批分析完成 - 积极: ${(finalPositive * 100).toFixed(1)}%, 消极: ${(finalNegative * 100).toFixed(1)}%, 中性: ${(finalNeutral * 100).toFixed(1)}%`);

  return NextResponse.json({
    positive: finalPositive,
    negative: finalNegative,
    neutral: finalNeutral
  });
}

// 处理大量评论：智能采样+AI分析
async function processLargeCommentsWithSampling(
  comments: string[],
  apiKey: string,
  apiEndpoint: string,
  modelName: string
): Promise<NextResponse> {
  console.log(`📝 智能采样分析 - 评论数量: ${comments.length}`);

  // 智能采样策略：保留代表性评论
  const sampledComments = intelligentSampling(comments, 100); // 采样100条代表性评论
  console.log(`📝 采样后评论数量: ${sampledComments.length}`);

  // 对采样的评论进行AI分析
  const aiResult = await processCommentsWithAI(sampledComments, apiKey, apiEndpoint, modelName);
  const aiData = await aiResult.json();

  // 对全量评论进行关键词统计作为补充
  const keywordResult = await fallbackSentimentAnalysis(comments);

  // 混合结果：AI分析权重70%，关键词统计权重30%
  const finalPositive = aiData.positive * 0.7 + keywordResult.positive * 0.3;
  const finalNegative = aiData.negative * 0.7 + keywordResult.negative * 0.3;
  const finalNeutral = aiData.neutral * 0.7 + keywordResult.neutral * 0.3;

  console.log(`✅ 混合分析完成 - AI权重70%, 关键词权重30%`);
  console.log(`📊 最终结果 - 积极: ${(finalPositive * 100).toFixed(1)}%, 消极: ${(finalNegative * 100).toFixed(1)}%, 中性: ${(finalNeutral * 100).toFixed(1)}%`);

  return NextResponse.json({
    positive: finalPositive,
    negative: finalNegative,
    neutral: finalNeutral
  });
}

// 智能采样函数：保留代表性评论
function intelligentSampling(comments: string[], targetCount: number): string[] {
  if (comments.length <= targetCount) {
    return comments;
  }

  // 按长度分类
  const short = comments.filter(c => c.length <= 10);
  const medium = comments.filter(c => c.length > 10 && c.length <= 30);
  const long = comments.filter(c => c.length > 30);

  // 按比例采样
  const shortSample = Math.floor(targetCount * 0.3);
  const mediumSample = Math.floor(targetCount * 0.5);
  const longSample = targetCount - shortSample - mediumSample;

  const result = [
    ...randomSample(short, Math.min(shortSample, short.length)),
    ...randomSample(medium, Math.min(mediumSample, medium.length)),
    ...randomSample(long, Math.min(longSample, long.length))
  ];

  // 如果还不够，随机补充
  if (result.length < targetCount) {
    const remaining = comments.filter(c => !result.includes(c));
    result.push(...randomSample(remaining, targetCount - result.length));
  }

  return result.slice(0, targetCount);
}

// 随机采样函数
function randomSample<T>(array: T[], count: number): T[] {
  const shuffled = [...array].sort(() => 0.5 - Math.random());
  return shuffled.slice(0, count);
}

// 调用AI API的通用函数
async function callAIAPI(
  prompt: string,
  apiKey: string,
  apiEndpoint: string,
  modelName: string,
  comments: string[]
): Promise<NextResponse> {
  console.log(`🔧 API调用信息:`);
  console.log(`- API端点: ${apiEndpoint}`);
  console.log(`- 模型名称: ${modelName}`);
  console.log(`- API密钥前缀: ${apiKey.substring(0, 10)}...`);

  const requestBody = {
    model: modelName,
    input: {
      messages: [{
        role: 'user',
        content: prompt
      }]
    },
    parameters: {
      result_format: 'message',
      temperature: 0.1,
      top_p: 0.8,
      max_tokens: 500
    }
  };

  console.log(`🔧 请求体:`, JSON.stringify(requestBody, null, 2));

  const response = await fetch(apiEndpoint, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${apiKey}`
    },
    body: JSON.stringify(requestBody)
  });

  if (!response.ok) {
    const errorText = await response.text();
    const errorInfo = `❌ AI API调用失败: ${response.status} ${response.statusText}`;
    console.error(errorInfo);
    console.error(`❌ 错误详情: ${errorText}`);
    throw new Error(`${errorInfo} - ${errorText}`);
  }

  const data = await response.json();
  const content = data.output?.choices?.[0]?.message?.content || data.output?.text || '';

  if (content) {
    try {
      console.log(`=== 服务端AI情感分析返回内容 ===`);
      console.log(`原始内容: ${content}`);

      // 数据清理
      let cleanedContent = content.trim();

      // 移除可能的BOM标记
      cleanedContent = cleanedContent.replace(/^\uFEFF/, '');

      // 处理markdown代码块格式
      const jsonMatch = cleanedContent.match(/```json\s*([\s\S]*?)\s*```/);
      if (jsonMatch) {
        cleanedContent = jsonMatch[1].trim();
      }

      // 移除前后引号
      if (cleanedContent.startsWith('"') && cleanedContent.endsWith('"')) {
        cleanedContent = cleanedContent.slice(1, -1);
      }

      // 尝试解析JSON
      const sentimentData = JSON.parse(cleanedContent);

      // 验证数据格式
      if (typeof sentimentData.positiveRatio === 'number' &&
          typeof sentimentData.negativeRatio === 'number' &&
          typeof sentimentData.neutralRatio === 'number') {

        console.log('✅ 服务端AI情感分析成功');
        return NextResponse.json({
          positive: sentimentData.positiveRatio,
          negative: sentimentData.negativeRatio,
          neutral: sentimentData.neutralRatio
        });
      } else {
        throw new Error('Invalid sentiment data format');
      }

    } catch (parseError) {
      console.error(`❌ 服务端AI返回内容解析失败:`, parseError);
      console.error(`原始内容: ${content}`);

      // 回退到关键词匹配
      return NextResponse.json(
        await fallbackSentimentAnalysis(comments)
      );
    }
  }

  // 如果没有内容，回退到关键词匹配
  return NextResponse.json(
    await fallbackSentimentAnalysis(comments)
  );
}

// 分批处理的AI API调用
async function callAIAPIForBatch(
  prompt: string,
  apiKey: string,
  apiEndpoint: string,
  modelName: string
): Promise<any> {
  try {
    const response = await fetch(apiEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify({
        model: modelName,
        input: {
          messages: [{
            role: 'user',
            content: prompt
          }]
        },
        parameters: {
          result_format: 'message',
          temperature: 0.1,
          top_p: 0.8,
          max_tokens: 500
        }
      })
    });

    if (!response.ok) {
      throw new Error(`API调用失败: ${response.status}`);
    }

    const data = await response.json();
    const content = data.output?.choices?.[0]?.message?.content || data.output?.text || '';

    if (content) {
      let cleanedContent = content.trim();
      cleanedContent = cleanedContent.replace(/^\uFEFF/, '');

      const jsonMatch = cleanedContent.match(/```json\s*([\s\S]*?)\s*```/);
      if (jsonMatch) {
        cleanedContent = jsonMatch[1].trim();
      }

      if (cleanedContent.startsWith('"') && cleanedContent.endsWith('"')) {
        cleanedContent = cleanedContent.slice(1, -1);
      }

      const sentimentData = JSON.parse(cleanedContent);
      return sentimentData;
    }

    return null;
  } catch (error) {
    console.error('批处理AI调用失败:', error);
    return null;
  }
}

// 关键词匹配的回退方案
async function fallbackSentimentAnalysis(comments: string[]): Promise<{ positive: number; negative: number; neutral: number }> {
  const positiveKeywords = ['好', '棒', '赞', '喜欢', '爱', '优秀', '完美', '推荐', '不错', '很棒', '厉害', '学习', '有用', '感谢', '分享', '支持', '加油', '期待', '好看', '美丽', '精致', '质量好'];
  const negativeKeywords = ['差', '烂', '垃圾', '骗', '假', '失望', '后悔', '不好', '糟糕', '坑', '骗人', '虚假', '夸张', '不推荐', '别买', '踩雷', '质量差', '不值', '浪费钱', '退货', '差评', '吐槽', '无语'];

  let positiveCount = 0;
  let negativeCount = 0;
  let neutralCount = 0;

  for (const comment of comments) {
    const hasPositive = positiveKeywords.some(keyword => comment.includes(keyword));
    const hasNegative = negativeKeywords.some(keyword => comment.includes(keyword));

    if (hasPositive && !hasNegative) {
      positiveCount++;
    } else if (hasNegative && !hasPositive) {
      negativeCount++;
    } else {
      neutralCount++;
    }
  }

  const total = positiveCount + negativeCount + neutralCount;
  if (total === 0) {
    return { positive: 0, negative: 0, neutral: 1 };
  }

  console.log('🔄 使用关键词匹配分析结果');
  return {
    positive: positiveCount / total,
    negative: negativeCount / total,
    neutral: neutralCount / total
  };
}
