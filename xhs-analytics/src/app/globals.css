@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* 打印样式 */
@media print {
  /* 隐藏不需要打印的元素 */
  .no-print {
    display: none !important;
  }

  /* 页面设置 */
  @page {
    margin: 1cm;
    size: A4;
  }

  /* 基础样式 */
  body {
    background: white !important;
    color: black !important;
    font-size: 12pt;
    line-height: 1.4;
  }

  /* 标题样式 */
  h1 {
    font-size: 18pt;
    font-weight: bold;
    margin-bottom: 12pt;
    page-break-after: avoid;
  }

  h2 {
    font-size: 16pt;
    font-weight: bold;
    margin-top: 16pt;
    margin-bottom: 8pt;
    page-break-after: avoid;
  }

  h3 {
    font-size: 14pt;
    font-weight: bold;
    margin-top: 12pt;
    margin-bottom: 6pt;
    page-break-after: avoid;
  }

  /* 段落样式 */
  p {
    margin-bottom: 6pt;
    orphans: 3;
    widows: 3;
  }

  /* 表格样式 */
  table {
    border-collapse: collapse;
    width: 100%;
    margin-bottom: 12pt;
  }

  th, td {
    border: 1px solid #000;
    padding: 4pt;
    text-align: left;
  }

  th {
    background-color: #f0f0f0 !important;
    font-weight: bold;
  }

  /* 图表容器 */
  .chart-container {
    page-break-inside: avoid;
    margin-bottom: 12pt;
  }

  /* 分析结果卡片 */
  .analysis-card {
    border: 1px solid #ccc;
    padding: 8pt;
    margin-bottom: 12pt;
    page-break-inside: avoid;
  }

  /* 强制分页 */
  .page-break {
    page-break-before: always;
  }

  /* 避免分页 */
  .no-page-break {
    page-break-inside: avoid;
  }

  /* 隐藏背景色和阴影 */
  * {
    background: transparent !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }

  /* 链接样式 */
  a {
    color: black !important;
    text-decoration: underline;
  }

  /* 导航栏在打印时隐藏 */
  .sticky {
    position: static !important;
  }
}
