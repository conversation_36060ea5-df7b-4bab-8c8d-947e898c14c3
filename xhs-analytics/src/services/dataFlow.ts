import { XHSPost, DerivedMetrics, AnalyticsResult, AuthorAnalysis, ContentCategory, DataFlowState, AIAnalysisResult } from '@/types';
import { AIService } from './ai';

export class DataFlowManager {
  private aiService: AIService;
  private state: DataFlowState = {
    status: 'idle',
    progress: 0
  };
  private listeners: Array<(state: DataFlowState) => void> = [];

  constructor() {
    this.aiService = new AIService();
  }

  subscribe(listener: (state: DataFlowState) => void) {
    this.listeners.push(listener);
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  private updateState(newState: Partial<DataFlowState>) {
    this.state = { ...this.state, ...newState };
    this.listeners.forEach(listener => listener(this.state));
  }

  async processData(posts: XHSPost[]): Promise<DataFlowState> {
    try {
      this.updateState({ status: 'loading', progress: 0, error: undefined });

      const cleanedPosts = this.cleanData(posts);
      this.updateState({ progress: 10 });

      const basicAnalysis = this.performBasicAnalysis(cleanedPosts);
      this.updateState({ progress: 20 });

      const authorAnalysis = await this.analyzeAuthors(cleanedPosts);
      this.updateState({ progress: 30 });

      const categoryAnalysis = this.analyzeCategories(cleanedPosts);
      this.updateState({ progress: 40 });

      // 开始AI深度分析
      this.updateState({ status: 'ai-analyzing', progress: 50 });
      const aiResults = await this.performAllAIAnalyses(cleanedPosts);

      this.updateState({
        status: 'completed',
        progress: 100,
        results: {
          basic: basicAnalysis,
          authors: authorAnalysis,
          categories: categoryAnalysis,
          aiAnalysis: aiResults
        }
      });

      return this.state;
    } catch (error) {
      this.updateState({
        status: 'error',
        error: error instanceof Error ? error.message : '数据处理失败'
      });
      throw error;
    }
  }

  async performAllAIAnalyses(posts: XHSPost[]): Promise<any[]> {
    const analysisTypes = [
      { id: 'insight', title: '深度洞察分析' },
      { id: 'recommendation', title: '创作者推荐分析' },
      { id: 'strategy', title: '营销策略分析' },
      { id: 'trend', title: '趋势预测分析' },
      { id: 'performance', title: '效果评估分析' },
      { id: 'optimization', title: '优化建议分析' }
    ];

    const results = [];
    const baseProgress = 50; // AI分析从50%开始
    const aiProgressRange = 50; // AI分析占50%的进度

    for (let i = 0; i < analysisTypes.length; i++) {
      const analysisType = analysisTypes[i];
      try {
        console.log(`🤖 开始执行 ${analysisType.title} (${i + 1}/${analysisTypes.length})...`);

        // 更新进度
        const currentProgress = baseProgress + (i / analysisTypes.length) * aiProgressRange;
        this.updateState({
          status: 'ai-analyzing',
          progress: Math.round(currentProgress),
          currentTask: `正在执行${analysisType.title}...`
        });

        // 使用fetch调用API而不是直接调用AIService
        const response = await fetch('/api/ai-analysis', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            posts: posts.slice(0, 50),
            analysisType: analysisType.id
          })
        });

        if (response.ok) {
          const result = await response.json();
          results.push({ ...result, analysisType: analysisType.id, title: analysisType.title });
          console.log(`✅ ${analysisType.title} 完成 (${i + 1}/${analysisTypes.length})`);
        } else {
          console.error(`❌ ${analysisType.title}失败: HTTP ${response.status}`);
        }

        // 串行执行，每个分析之间间隔3秒
        if (i < analysisTypes.length - 1) {
          console.log(`⏳ 等待3秒后执行下一个分析...`);
          await new Promise(resolve => setTimeout(resolve, 3000));
        }
      } catch (error) {
        console.error(`❌ ${analysisType.title}失败:`, error);
        // 即使失败也继续下一个分析
      }
    }

    console.log('🎉 所有AI分析完成！');
    return results;
  }

  async performAIAnalysis(posts: XHSPost[], analysisType: 'insight' | 'recommendation' | 'strategy'): Promise<AIAnalysisResult> {
    try {
      this.updateState({ status: 'analyzing', progress: 0 });
      
      const result = await this.aiService.analyzeContent(posts, analysisType);
      
      this.updateState({ 
        status: 'completed',
        progress: 100,
        results: {
          ...this.state.results!,
          ai: result
        }
      });
      
      return result;
    } catch (error) {
      this.updateState({ 
        status: 'error', 
        error: error instanceof Error ? error.message : 'AI分析失败' 
      });
      throw error;
    }
  }

  private cleanData(posts: XHSPost[]): XHSPost[] {
    return posts.filter(post => {
      return post.软文标题 && 
             post.软文作者 && 
             post.点赞数 >= 0 && 
             post.收藏数 >= 0 && 
             post.评论数 >= 0;
    }).map(post => ({
      ...post,
      点赞数: Math.max(0, post.点赞数 || 0),
      收藏数: Math.max(0, post.收藏数 || 0),
      评论数: Math.max(0, post.评论数 || 0),
      粉丝: Math.max(0, post.粉丝 || 0),
      评论: post.评论 || [],
      软文标签: post.软文标签 || []
    }));
  }

  private performBasicAnalysis(posts: XHSPost[]): AnalyticsResult {
    const totalLikes = posts.reduce((sum, post) => sum + post.点赞数, 0);
    const totalFavorites = posts.reduce((sum, post) => sum + post.收藏数, 0);
    const totalComments = posts.reduce((sum, post) => sum + post.评论数, 0);

    const topLikedPost = posts.reduce((max, post) => post.点赞数 > max.点赞数 ? post : max, posts[0]);
    const topFavoritedPost = posts.reduce((max, post) => post.收藏数 > max.收藏数 ? post : max, posts[0]);
    const topCommentedPost = posts.reduce((max, post) => post.评论数 > max.评论数 ? post : max, posts[0]);

    return {
      总笔记数: posts.length,
      总点赞数: totalLikes,
      总收藏数: totalFavorites,
      总评论数: totalComments,
      平均点赞数: Math.round(totalLikes / posts.length),
      平均收藏数: Math.round(totalFavorites / posts.length),
      平均评论数: Math.round(totalComments / posts.length),
      最高点赞笔记: topLikedPost,
      最高收藏笔记: topFavoritedPost,
      最高评论笔记: topCommentedPost
    };
  }

  private async analyzeAuthors(posts: XHSPost[]): Promise<AuthorAnalysis[]> {
    const authorMap = new Map<string, XHSPost[]>();
    
    posts.forEach(post => {
      if (!authorMap.has(post.软文作者)) {
        authorMap.set(post.软文作者, []);
      }
      authorMap.get(post.软文作者)!.push(post);
    });

    // 改为串行处理，避免并行调用API导致失败
    const results: AuthorAnalysis[] = [];
    for (const [author, authorPosts] of authorMap.entries()) {
      const totalLikes = authorPosts.reduce((sum, post) => sum + post.点赞数, 0);
      const totalFavorites = authorPosts.reduce((sum, post) => sum + post.收藏数, 0);
      const totalComments = authorPosts.reduce((sum, post) => sum + post.评论数, 0);
      const followers = authorPosts[0].粉丝 || 0;
      
      // 情感分析：基于评论关键词（使用通义千问AI分析）
      const commentSentiment = await this.analyzeCommentSentiment(authorPosts);
      
      const businessValue = this.calculateBusinessValue(
        totalLikes, 
        totalFavorites, 
        totalComments, 
        followers, 
        authorPosts.length,
        commentSentiment
      );

      results.push({
        作者: author,
        笔记数: authorPosts.length,
        总点赞数: totalLikes,
        总收藏数: totalFavorites,
        总评论数: totalComments,
        积极情感度: Math.round(commentSentiment.positive * 100), // AI分析结果
        粉丝数: followers,
        商业价值评分: businessValue
      });
    }

    return results.sort((a, b) => b.商业价值评分 - a.商业价值评分);
  }

  private analyzeCategories(posts: XHSPost[]): ContentCategory[] {
    const categoryMap = new Map<string, XHSPost[]>();
    
    posts.forEach(post => {
      const category = this.categorizeContent(post);
      if (!categoryMap.has(category)) {
        categoryMap.set(category, []);
      }
      categoryMap.get(category)!.push(post);
    });

    return Array.from(categoryMap.entries()).map(([category, categoryPosts]) => {
      const totalLikes = categoryPosts.reduce((sum, post) => sum + post.点赞数, 0);
      const totalFavorites = categoryPosts.reduce((sum, post) => sum + post.收藏数, 0);
      const totalComments = categoryPosts.reduce((sum, post) => sum + post.评论数, 0);
      const avgEngagement = (totalLikes + totalFavorites + totalComments) / categoryPosts.length;

      return {
        分类: category,
        笔记数: categoryPosts.length,
        总点赞数: totalLikes,
        总收藏数: totalFavorites,
        总评论数: totalComments,
        平均互动数: Math.round(avgEngagement)
      };
    }).sort((a, b) => b.平均互动数 - a.平均互动数);
  }

  private categorizeContent(post: XHSPost): string {
    const content = post.软文内容.toLowerCase();
    const title = post.软文标题.toLowerCase();
    const text = `${title} ${content}`;

    if (text.includes('教程') || text.includes('教学') || text.includes('怎么') || text.includes('如何')) {
      return '教程类';
    } else if (text.includes('体验') || text.includes('试') || text.includes('用') || text.includes('测评')) {
      return '体验类';
    } else if (text.includes('推荐') || text.includes('分享') || text.includes('种草')) {
      return '推荐类';
    } else if (text.includes('攻略') || text.includes('指南') || text.includes('秘籍')) {
      return '攻略类';
    } else if (text.includes('日常') || text.includes('生活') || text.includes('记录')) {
      return '生活类';
    } else {
      return '其他';
    }
  }

  private calculateBusinessValue(likes: number, favorites: number, comments: number, followers: number, postCount: number, commentSentiment?: { positive: number; negative: number; neutral: number }): number {
    const totalEngagement = likes + favorites + comments;

    // 处理缺失粉丝数的情况
    const effectiveFollowers = this.getEffectiveFollowers(followers, totalEngagement);

    // 1. ROI效率：同样互动数下，粉丝数越少ROI越高
    const roiEfficiency = this.calculateROIEfficiency(totalEngagement, effectiveFollowers);

    // 2. 互动率：总互动数/粉丝数，标准化到0-1范围
    const engagementRate = effectiveFollowers > 0 ? Math.min(totalEngagement / effectiveFollowers, 1) : 0;

    // 3. 平均互动：每篇内容的平均互动数，用对数标准化
    const avgEngagement = postCount > 0 ? totalEngagement / postCount : 0;
    const normalizedAvgEngagement = Math.min(avgEngagement / 1000, 1); // 假设1000互动为满分

    // 4. 内容质量：收藏/点赞比率，反映内容实用性
    const contentQuality = likes > 0 ? Math.min(favorites / likes, 2) : 0; // 允许超过1，收藏可能多于点赞

    // 5. 粉丝规模影响：适度的粉丝数有助于传播，但不是主导因素
    const followerScale = this.calculateFollowerScale(effectiveFollowers);

    // 6. 情感评分：基于评论的正负面情感比例
    const sentimentScore = this.calculateSentimentScore(commentSentiment, comments);

    // 综合评分：重新调整权重，突出ROI效率
    const score = (
      roiEfficiency * 0.35 +           // ROI效率 35% (新增，最重要)
      engagementRate * 0.25 +          // 互动率 25%
      normalizedAvgEngagement * 0.15 + // 平均互动 15%
      contentQuality * 0.15 +          // 内容质量 15%
      followerScale * 0.05 +           // 粉丝规模 5% (降低权重)
      sentimentScore * 0.05            // 情感分析 5%
    ) * 100;

    console.log(`📊 商业价值计算详情 - 粉丝:${effectiveFollowers}, 互动:${totalEngagement}, ROI效率:${(roiEfficiency*100).toFixed(1)}%, 最终得分:${Math.round(score)}`);

    return Math.min(100, Math.max(0, Math.round(score)));
  }

  // 处理缺失粉丝数的情况
  private getEffectiveFollowers(followers: number, totalEngagement: number): number {
    if (followers > 0) {
      return followers;
    }

    // 如果粉丝数缺失，根据互动数估算
    // 一般互动率在1-10%之间，我们取5%作为估算基准
    const estimatedFollowers = Math.max(totalEngagement / 0.05, 100); // 最少100粉丝
    console.log(`⚠️ 粉丝数缺失，根据互动数${totalEngagement}估算为${Math.round(estimatedFollowers)}`);
    return estimatedFollowers;
  }

  // 计算ROI效率：同样互动数下，粉丝数越少ROI越高
  private calculateROIEfficiency(totalEngagement: number, followers: number): number {
    if (totalEngagement === 0) return 0;

    // ROI效率 = 互动数 / (粉丝数的平方根)
    // 使用平方根可以平衡粉丝数的影响，避免过度惩罚大粉丝
    const efficiency = totalEngagement / Math.sqrt(followers);

    // 标准化到0-1范围，假设效率值100为满分
    return Math.min(efficiency / 100, 1);
  }

  // 计算粉丝规模影响：适度的粉丝数有助于传播
  private calculateFollowerScale(followers: number): number {
    if (followers <= 0) return 0;

    // 使用对数函数，让粉丝数的边际效应递减
    // 1万粉丝 ≈ 0.5分，10万粉丝 ≈ 0.7分，100万粉丝 ≈ 0.9分
    return Math.min(Math.log10(followers + 1) / 6, 1);
  }

  // 计算情感评分
  private calculateSentimentScore(commentSentiment?: { positive: number; negative: number; neutral: number }, comments?: number): number {
    let sentimentScore = 0.5; // 默认中性

    if (commentSentiment && comments && comments > 0) {
      const total = commentSentiment.positive + commentSentiment.negative + commentSentiment.neutral;
      if (total > 0) {
        const positiveRatio = commentSentiment.positive / total;
        const negativeRatio = commentSentiment.negative / total;

        // 正面情感增加评分，负面情感大幅降低评分
        sentimentScore = Math.max(0, Math.min(1, positiveRatio * 1.5 - negativeRatio * 2 + 0.3));
      }
    }

    return sentimentScore;
  }

  getState(): DataFlowState {
    return this.state;
  }

  /**
   * 使用通义千问AI进行评论情感分析
   */
  private async analyzeCommentSentiment(posts: XHSPost[]): Promise<{ positive: number; negative: number; neutral: number }> {
    const allComments = posts.flatMap(post => post.评论 || []);
    
    console.log('🔍 analyzeCommentSentiment 调试信息:');
    console.log('- 处理的posts数量:', posts.length);
    console.log('- 提取的comments数量:', allComments.length);
    console.log('- 前3条评论:', allComments.slice(0, 3));
    console.log('');
    
    if (allComments.length === 0) {
      console.log('⚠️ 评论为空，返回默认值');
      return { positive: 0, negative: 0, neutral: 1 };
    }
    
    // 🔧 简化评论处理：统一转为字符串，并清理格式
    const cleanedComments = allComments.map(comment => {
      // 统一转为字符串
      let commentStr = String(comment || '').trim();
      
      // 清理各种格式问题
      // 移除数组括号和引号：['评论'] → 评论
      if (commentStr.startsWith("['") && commentStr.endsWith("']")) {
        commentStr = commentStr.slice(2, -2);
      } else if (commentStr.startsWith('["') && commentStr.endsWith('"]')) {
        commentStr = commentStr.slice(2, -2);
      } else if (commentStr.startsWith("'") && commentStr.endsWith("'")) {
        commentStr = commentStr.slice(1, -1);
      } else if (commentStr.startsWith('"') && commentStr.endsWith('"')) {
        commentStr = commentStr.slice(1, -1);
      }
      
      // 处理不完整的格式，如 "['好香'" 或 "'好香" 等
      if (commentStr.startsWith("['")) {
        commentStr = commentStr.slice(2); // 移除 ['
      } else if (commentStr.startsWith("'")) {
        commentStr = commentStr.slice(1); // 移除 '
      } else if (commentStr.startsWith('"')) {
        commentStr = commentStr.slice(1); // 移除 "
      }
      
      if (commentStr.endsWith("']")) {
        commentStr = commentStr.slice(0, -2); // 移除 ']
      } else if (commentStr.endsWith("'")) {
        commentStr = commentStr.slice(0, -1); // 移除 '
      } else if (commentStr.endsWith('"')) {
        commentStr = commentStr.slice(0, -1); // 移除 "
      }
      
      return commentStr;
    }).filter(comment => comment.length > 0);
    
    console.log('🧹 清理后的comments数量:', cleanedComments.length);
    console.log('🧹 清理后的前3条评论:', cleanedComments.slice(0, 3));
    console.log('');
    
    try {
      // 调用服务端API进行情感分析，避免CORS问题
      console.log('📝 调用服务端情感分析API');
      console.log('📝 评论数量:', cleanedComments.length);
      console.log('');

      const response = await fetch('/api/sentiment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          comments: cleanedComments // 发送全量评论，让服务端智能处理
        })
      });

      if (!response.ok) {
        const errorInfo = `❌ 情感分析API调用失败: ${response.status} ${response.statusText}`;
        console.error(errorInfo);
        throw new Error(errorInfo);
      }

      const sentimentResult = await response.json();
      console.log('✅ 服务端情感分析成功:', sentimentResult);

      if (sentimentResult.error) {
        throw new Error(sentimentResult.error);
      }

      // 验证返回数据格式
      if (typeof sentimentResult.positive === 'number' &&
          typeof sentimentResult.negative === 'number' &&
          typeof sentimentResult.neutral === 'number') {

        // 确保比例总和为1
        const sum = sentimentResult.positive + sentimentResult.negative + sentimentResult.neutral;
        if (sum > 0) {
          const positive = sentimentResult.positive / sum;
          const negative = sentimentResult.negative / sum;
          const neutral = sentimentResult.neutral / sum;

          console.log(`📊 最终情感分析结果: 积极${(positive * 100).toFixed(1)}% 消极${(negative * 100).toFixed(1)}% 中性${(neutral * 100).toFixed(1)}%`);

          return { positive, negative, neutral };
        }
      }

      throw new Error('Invalid sentiment analysis result format');

    } catch (error) {
      console.warn('服务端情感分析失败，使用关键词匹配:', error);
      // AI分析失败时回退到关键词匹配
      return this.fallbackSentimentAnalysis(posts);
    }
  }
  
  // 关键词匹配的回退方案
  private fallbackSentimentAnalysis(posts: XHSPost[]): { positive: number; negative: number; neutral: number } {
    const positiveKeywords = ['好', '棒', '赞', '喜欢', '爱', '优秀', '完美', '推荐', '不错', '很棒', '厉害', '学习', '有用', '感谢', '分享', '支持', '加油', '期待', '好看', '美丽', '精致', '质量好'];
    const negativeKeywords = ['差', '烂', '垃圾', '骗', '假', '失望', '后悔', '不好', '糟糕', '坑', '骗人', '虚假', '夸张', '不推荐', '别买', '踩雷', '质量差', '不值', '浪费钱', '退货', '差评', '吐槽', '无语'];
    
    let positiveCount = 0;
    let negativeCount = 0;
    let neutralCount = 0;
    
    posts.forEach(post => {
      if (post.评论 && Array.isArray(post.评论)) {
        post.评论.forEach(comment => {
          // 确保comment是字符串类型，并进行安全转换
          let commentText = '';
          try {
            if (typeof comment === 'string') {
              commentText = comment;
            } else if (comment !== null && comment !== undefined) {
              commentText = String(comment);
            } else {
              return; // 跳过无效评论
            }
            
            // 清理评论文本格式 (与AI分析中的清理逻辑保持一致)
            if (commentText.startsWith("['") && commentText.endsWith("']")) {
              commentText = commentText.slice(2, -2);
            } else if (commentText.startsWith('["') && commentText.endsWith('"]')) {
              commentText = commentText.slice(2, -2);
            } else if (commentText.startsWith("'") && commentText.endsWith("'")) {
              commentText = commentText.slice(1, -1);
            } else if (commentText.startsWith('"') && commentText.endsWith('"')) {
              commentText = commentText.slice(1, -1);
            }
            
            // 处理特殊情况：只有开头的 [' 但没有结尾的 ']
            if (commentText.startsWith("['") && !commentText.endsWith("']")) {
              commentText = commentText.slice(2);
            }
            
            // 转为小写进行关键词匹配
            commentText = commentText.toLowerCase();
          } catch (err) {
            console.warn(`评论格式转换失败，跳过该评论: ${comment}`, err);
            return; // 跳过有问题的评论
          }
          
          let hasPositive = false;
          let hasNegative = false;
          
          // 检查正面关键词
          for (const keyword of positiveKeywords) {
            if (commentText.includes(keyword)) {
              hasPositive = true;
              break;
            }
          }
          
          // 检查负面关键词
          for (const keyword of negativeKeywords) {
            if (commentText.includes(keyword)) {
              hasNegative = true;
              break;
            }
          }
          
          // 分类统计
          if (hasPositive && !hasNegative) {
            positiveCount++;
          } else if (hasNegative && !hasPositive) {
            negativeCount++;
          } else {
            neutralCount++;
          }
        });
      }
    });
    
    const total = positiveCount + negativeCount + neutralCount;
    
    if (total === 0) {
      return { positive: 0, negative: 0, neutral: 1 };
    }
    
    return {
      positive: positiveCount / total,
      negative: negativeCount / total,
      neutral: neutralCount / total
    };
  }
}