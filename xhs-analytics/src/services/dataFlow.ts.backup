import { XHSPost, DerivedMetrics, AnalyticsResult, AuthorAnalysis, ContentCategory, DataFlowState, AIAnalysisResult } from '@/types';
import { AIService } from './ai';

export class DataFlowManager {
  private aiService: AIService;
  private state: DataFlowState = {
    status: 'idle',
    progress: 0
  };
  private listeners: Array<(state: DataFlowState) => void> = [];

  constructor() {
    this.aiService = new AIService();
  }

  subscribe(listener: (state: DataFlowState) => void) {
    this.listeners.push(listener);
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  private updateState(newState: Partial<DataFlowState>) {
    this.state = { ...this.state, ...newState };
    this.listeners.forEach(listener => listener(this.state));
  }

  async processData(posts: XHSPost[]): Promise<DataFlowState> {
    try {
      this.updateState({ status: 'loading', progress: 0, error: undefined });
      
      const cleanedPosts = this.cleanData(posts);
      this.updateState({ progress: 20 });
      
      const basicAnalysis = this.performBasicAnalysis(cleanedPosts);
      this.updateState({ progress: 40 });
      
      const authorAnalysis = await this.analyzeAuthors(cleanedPosts);
      this.updateState({ progress: 60 });
      
      const categoryAnalysis = this.analyzeCategories(cleanedPosts);
      this.updateState({ progress: 80 });
      
      this.updateState({
        status: 'completed',
        progress: 100,
        results: {
          basic: basicAnalysis,
          authors: authorAnalysis,
          categories: categoryAnalysis
        }
      });
      
      return this.state;
    } catch (error) {
      this.updateState({ 
        status: 'error', 
        error: error instanceof Error ? error.message : '数据处理失败' 
      });
      throw error;
    }
  }

  async performAIAnalysis(posts: XHSPost[], analysisType: 'insight' | 'recommendation' | 'strategy'): Promise<AIAnalysisResult> {
    try {
      this.updateState({ status: 'analyzing', progress: 0 });
      
      const result = await this.aiService.analyzeContent(posts, analysisType);
      
      this.updateState({ 
        status: 'completed',
        progress: 100,
        results: {
          ...this.state.results!,
          ai: result
        }
      });
      
      return result;
    } catch (error) {
      this.updateState({ 
        status: 'error', 
        error: error instanceof Error ? error.message : 'AI分析失败' 
      });
      throw error;
    }
  }

  private cleanData(posts: XHSPost[]): XHSPost[] {
    return posts.filter(post => {
      return post.软文标题 && 
             post.软文作者 && 
             post.点赞数 >= 0 && 
             post.收藏数 >= 0 && 
             post.评论数 >= 0;
    }).map(post => ({
      ...post,
      点赞数: Math.max(0, post.点赞数 || 0),
      收藏数: Math.max(0, post.收藏数 || 0),
      评论数: Math.max(0, post.评论数 || 0),
      粉丝: Math.max(0, post.粉丝 || 0),
      评论: post.评论 || [],
      软文标签: post.软文标签 || []
    }));
  }

  private performBasicAnalysis(posts: XHSPost[]): AnalyticsResult {
    const totalLikes = posts.reduce((sum, post) => sum + post.点赞数, 0);
    const totalFavorites = posts.reduce((sum, post) => sum + post.收藏数, 0);
    const totalComments = posts.reduce((sum, post) => sum + post.评论数, 0);

    const topLikedPost = posts.reduce((max, post) => post.点赞数 > max.点赞数 ? post : max, posts[0]);
    const topFavoritedPost = posts.reduce((max, post) => post.收藏数 > max.收藏数 ? post : max, posts[0]);
    const topCommentedPost = posts.reduce((max, post) => post.评论数 > max.评论数 ? post : max, posts[0]);

    return {
      总笔记数: posts.length,
      总点赞数: totalLikes,
      总收藏数: totalFavorites,
      总评论数: totalComments,
      平均点赞数: Math.round(totalLikes / posts.length),
      平均收藏数: Math.round(totalFavorites / posts.length),
      平均评论数: Math.round(totalComments / posts.length),
      最高点赞笔记: topLikedPost,
      最高收藏笔记: topFavoritedPost,
      最高评论笔记: topCommentedPost
    };
  }

  private async analyzeAuthors(posts: XHSPost[]): Promise<AuthorAnalysis[]> {
    const authorMap = new Map<string, XHSPost[]>();
    
    posts.forEach(post => {
      if (!authorMap.has(post.软文作者)) {
        authorMap.set(post.软文作者, []);
      }
      authorMap.get(post.软文作者)!.push(post);
    });

    // 改为串行处理，避免并行调用API导致失败
    const results: AuthorAnalysis[] = [];
    for (const [author, authorPosts] of authorMap.entries()) {
      const totalLikes = authorPosts.reduce((sum, post) => sum + post.点赞数, 0);
      const totalFavorites = authorPosts.reduce((sum, post) => sum + post.收藏数, 0);
      const totalComments = authorPosts.reduce((sum, post) => sum + post.评论数, 0);
      const followers = authorPosts[0].粉丝 || 0;
      
      // 情感分析：基于评论关键词（使用通义千问AI分析）
      const commentSentiment = await this.analyzeCommentSentiment(authorPosts);
      
      const businessValue = this.calculateBusinessValue(
        totalLikes, 
        totalFavorites, 
        totalComments, 
        followers, 
        authorPosts.length,
        commentSentiment
      );

      results.push({
        作者: author,
        笔记数: authorPosts.length,
        总点赞数: totalLikes,
        总收藏数: totalFavorites,
        总评论数: totalComments,
        积极情感度: Math.round(commentSentiment.positive * 100), // AI分析结果
        粉丝数: followers,
        商业价值评分: businessValue
      });
    }

    return results.sort((a, b) => b.商业价值评分 - a.商业价值评分).slice(0, 20);
  }

  private analyzeCategories(posts: XHSPost[]): ContentCategory[] {
    const categoryMap = new Map<string, XHSPost[]>();
    
    posts.forEach(post => {
      const category = this.categorizeContent(post);
      if (!categoryMap.has(category)) {
        categoryMap.set(category, []);
      }
      categoryMap.get(category)!.push(post);
    });

    return Array.from(categoryMap.entries()).map(([category, categoryPosts]) => {
      const totalLikes = categoryPosts.reduce((sum, post) => sum + post.点赞数, 0);
      const totalFavorites = categoryPosts.reduce((sum, post) => sum + post.收藏数, 0);
      const totalComments = categoryPosts.reduce((sum, post) => sum + post.评论数, 0);
      const avgEngagement = (totalLikes + totalFavorites + totalComments) / categoryPosts.length;

      return {
        分类: category,
        笔记数: categoryPosts.length,
        总点赞数: totalLikes,
        总收藏数: totalFavorites,
        总评论数: totalComments,
        平均互动数: Math.round(avgEngagement)
      };
    }).sort((a, b) => b.平均互动数 - a.平均互动数);
  }

  private categorizeContent(post: XHSPost): string {
    const content = post.软文内容.toLowerCase();
    const title = post.软文标题.toLowerCase();
    const text = `${title} ${content}`;

    if (text.includes('教程') || text.includes('教学') || text.includes('怎么') || text.includes('如何')) {
      return '教程类';
    } else if (text.includes('体验') || text.includes('试') || text.includes('用') || text.includes('测评')) {
      return '体验类';
    } else if (text.includes('推荐') || text.includes('分享') || text.includes('种草')) {
      return '推荐类';
    } else if (text.includes('攻略') || text.includes('指南') || text.includes('秘籍')) {
      return '攻略类';
    } else if (text.includes('日常') || text.includes('生活') || text.includes('记录')) {
      return '生活类';
    } else {
      return '其他';
    }
  }

  private calculateBusinessValue(likes: number, favorites: number, comments: number, followers: number, postCount: number, commentSentiment?: { positive: number; negative: number; neutral: number }): number {
    // 互动率：总互动数/粉丝数，标准化到0-1范围
    const totalEngagement = likes + favorites + comments;
    const engagementRate = followers > 0 ? Math.min(totalEngagement / followers, 1) : 0;
    
    // 平均互动：每篇内容的平均互动数，用对数标准化
    const avgEngagement = postCount > 0 ? totalEngagement / postCount : 0;
    const normalizedAvgEngagement = Math.min(avgEngagement / 1000, 1); // 假设1000互动为满分
    
    // 内容质量：收藏/点赞比率，反映内容实用性
    const contentQuality = likes > 0 ? Math.min(favorites / likes, 2) : 0; // 允许超过1，收藏可能多于点赞
    
    // 粉丝影响力：粉丝数量的对数标准化，避免大粉丝数主导
    const followerInfluence = followers > 0 ? Math.min(Math.log10(followers + 1) / 6, 1) : 0; // 假设100万粉丝为满分
    
    // 情感评分：基于评论的正负面情感比例
    let sentimentScore = 0.5; // 默认中性
    if (commentSentiment && comments > 0) {
      const positiveRatio = commentSentiment.positive / (commentSentiment.positive + commentSentiment.negative + commentSentiment.neutral);
      const negativeRatio = commentSentiment.negative / (commentSentiment.positive + commentSentiment.negative + commentSentiment.neutral);
      
      // 正面情感增加评分，负面情感大幅降低评分
      sentimentScore = Math.max(0, Math.min(1, positiveRatio * 1.5 - negativeRatio * 2 + 0.3));
    }
    
    // 综合评分：调整权重，让各指标更加平衡，情感分析占15%
    const score = (
      engagementRate * 0.30 +           // 互动率 30%
      normalizedAvgEngagement * 0.20 +   // 平均互动 20%
      contentQuality * 0.25 +          // 内容质量 25%
      followerInfluence * 0.10 +       // 粉丝影响力 10%
      sentimentScore * 0.15             // 情感分析 15%
    ) * 100;
    
    return Math.min(100, Math.max(0, Math.round(score)));
  }

  getState(): DataFlowState {
    return this.state;
  }

  /**
   * 使用通义千问AI进行评论情感分析
   */
  private async analyzeCommentSentiment(posts: XHSPost[]): Promise<{ positive: number; negative: number; neutral: number }> {
    const allComments = posts.flatMap(post => post.评论 || []);
    
    console.log('🔍 analyzeCommentSentiment 调试信息:');
    console.log('- 处理的posts数量:', posts.length);
    console.log('- 提取的comments数量:', allComments.length);
    console.log('- 前3条评论:', allComments.slice(0, 3));
    console.log('');
    
    if (allComments.length === 0) {
      console.log('⚠️ 评论为空，返回默认值');
      return { positive: 0, negative: 0, neutral: 1 };
    }
    
    // 🔧 简化评论处理：统一转为字符串，并清理格式
    const cleanedComments = allComments.map(comment => {
      // 统一转为字符串
      let commentStr = String(comment || '').trim();
      
      // 清理各种格式问题
      // 移除数组括号和引号：['评论'] → 评论
      if (commentStr.startsWith("['") && commentStr.endsWith("']")) {
        commentStr = commentStr.slice(2, -2);
      } else if (commentStr.startsWith('["') && commentStr.endsWith('"]')) {
        commentStr = commentStr.slice(2, -2);
      } else if (commentStr.startsWith("'") && commentStr.endsWith("'")) {
        commentStr = commentStr.slice(1, -1);
      } else if (commentStr.startsWith('"') && commentStr.endsWith('"')) {
        commentStr = commentStr.slice(1, -1);
      }
      
      // 处理不完整的格式，如 "['好香'" 或 "'好香" 等
      if (commentStr.startsWith("['")) {
        commentStr = commentStr.slice(2); // 移除 ['
      } else if (commentStr.startsWith("'")) {
        commentStr = commentStr.slice(1); // 移除 '
      } else if (commentStr.startsWith('"')) {
        commentStr = commentStr.slice(1); // 移除 "
      }
      
      if (commentStr.endsWith("']")) {
        commentStr = commentStr.slice(0, -2); // 移除 ']
      } else if (commentStr.endsWith("'")) {
        commentStr = commentStr.slice(0, -1); // 移除 '
      } else if (commentStr.endsWith('"')) {
        commentStr = commentStr.slice(0, -1); // 移除 "
      }
      
      return commentStr;
    }).filter(comment => comment.length > 0);
    
    console.log('🧹 清理后的comments数量:', cleanedComments.length);
    console.log('🧹 清理后的前3条评论:', cleanedComments.slice(0, 3));
    console.log('');
    
    // 临时使用更快的模型进行情感分析
    const originalModel = this.aiService['modelName'];
    this.aiService['modelName'] = 'qwen-turbo'; // 使用更快的模型
    
    try {
      // 使用现有的AI服务进行情感分析，使用更快的模型
      const sentimentPrompt = `请分析以下小红书评论的情感倾向，按照严格的JSON格式返回结果。

评论内容：
${cleanedComments.slice(0, 30).join('\n')}

请务必严格按照以下JSON格式返回，不要添加任何解释文字、markdown标记或其他内容：
{
  "positive": 积极评论数量,
  "negative": 消极评论数量,
  "neutral": 中性评论数量,
  "total": 总评论数,
  "positiveRatio": 积极比例(0到1之间的小数),
  "negativeRatio": 消极比例(0到1之间的小数),
  "neutralRatio": 中性比例(0到1之间的小数)
}

分析标准：
- 积极：好、棒、赞、喜欢、推荐、不错、很棒等正面表达
- 消极：差、烂、不好、失望、不推荐、质量差等负面表达
- 中性：一般描述、事实陈述、无明显情感倾向

要求：只返回纯JSON格式，不要添加任何其他文字。`;

      console.log('📝 发送的提示词长度:', sentimentPrompt.length);
      console.log('📝 提示词前100字符:', sentimentPrompt.substring(0, 100));
      console.log('');
      
      const response = await fetch(this.aiService['apiEndpoint'], {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.aiService['apiKey']}`
        },
        body: JSON.stringify({
          model: this.aiService['modelName'],
          input: {
            messages: [{
              role: 'user',
              content: sentimentPrompt
            }]
          },
          parameters: {
            result_format: 'message',
            temperature: 0.1, // 降低随机性，提高JSON格式稳定性
            top_p: 0.8,
            max_tokens: 500
          }
        })
      });

      if (!response.ok) {
        const errorInfo = `❌ AI API调用失败: ${response.status} ${response.statusText}`;
        console.error(errorInfo);
        throw new Error(errorInfo);
      }

      const result = await response.json();
      console.log(`📤 AI API完整响应:`);
      console.log(JSON.stringify(result, null, 2));
      
      const content = result.output?.choices?.[0]?.message?.content;
      
      if (!content) {
        const errorInfo = `❌ AI返回内容为空: ${JSON.stringify(result)}`;
        console.error(errorInfo);
        throw new Error(errorInfo);
      }
      
      if (content) {
        try {
          // 🔍 调试：打印AI返回的原始内容
          console.log(`=== AI情感分析返回内容 ===`);
          console.log(`原始内容: ${content}`);
          console.log(`内容类型: ${typeof content}`);
          console.log(`内容长度: ${content.length}`);
          
          // 数据清理：移除可能的问题字符
          let cleanedContent = content.trim();
          
          // 移除可能的BOM标记
          cleanedContent = cleanedContent.replace(/^\uFEFF/, '');
          
          // 修复1：处理markdown代码块格式
          const jsonMatch = cleanedContent.match(/```json\s*([\s\S]*?)\s*```/);
          if (jsonMatch) {
            cleanedContent = jsonMatch[1].trim();
            console.log('📝 提取markdown代码块:', cleanedContent);
          }
          
          // 修复2：移除前后引号
          if (cleanedContent.startsWith('"') && cleanedContent.endsWith('"')) {
            cleanedContent = cleanedContent.slice(1, -1);
            console.log('📝 移除前后引号:', cleanedContent);
          }
          
          // 修复3：处理转义字符
          cleanedContent = cleanedContent.replace(/\\"/g, '"').replace(/\\n/g, '\n');
          
          // 修复4：如果内容不是以{开头，尝试寻找JSON部分
          if (!cleanedContent.startsWith('{')) {
            const jsonStart = cleanedContent.indexOf('{');
            const jsonEnd = cleanedContent.lastIndexOf('}');
            if (jsonStart !== -1 && jsonEnd !== -1 && jsonEnd > jsonStart) {
              cleanedContent = cleanedContent.substring(jsonStart, jsonEnd + 1);
              console.log('📝 提取JSON部分:', cleanedContent);
            }
          }
          
          console.log('🔧 最终清理后的内容:', cleanedContent);
          
          // 尝试解析JSON
          const sentimentResult = JSON.parse(cleanedContent);
          console.log(`✅ JSON解析成功:`, sentimentResult);
          
          // 恢复原始模型设置
          this.aiService['modelName'] = originalModel;
          
          // 计算比例，支持多种返回格式
          let positive = 0, negative = 0, neutral = 0;
          
          if (sentimentResult.positiveRatio !== undefined) {
            // 如果AI直接返回比例
            positive = sentimentResult.positiveRatio;
            negative = sentimentResult.negativeRatio || 0;
            neutral = sentimentResult.neutralRatio || 0;
          } else if (sentimentResult.positive !== undefined && sentimentResult.total !== undefined) {
            // 如果AI返回数量和总数
            const total = sentimentResult.total || (sentimentResult.positive + sentimentResult.negative + sentimentResult.neutral);
            positive = total > 0 ? sentimentResult.positive / total : 0;
            negative = total > 0 ? (sentimentResult.negative || 0) / total : 0;
            neutral = total > 0 ? (sentimentResult.neutral || 0) / total : 1;
          } else {
            // 如果只返回数量，计算比例
            const total = (sentimentResult.positive || 0) + (sentimentResult.negative || 0) + (sentimentResult.neutral || 0);
            positive = total > 0 ? (sentimentResult.positive || 0) / total : 0;
            negative = total > 0 ? (sentimentResult.negative || 0) / total : 0;
            neutral = total > 0 ? (sentimentResult.neutral || 0) / total : 1;
          }
          
          // 确保比例总和为1
          const sum = positive + negative + neutral;
          if (sum > 0) {
            positive = positive / sum;
            negative = negative / sum;
            neutral = neutral / sum;
          }
          
          console.log(`📊 最终情感分析结果: 积极${(positive * 100).toFixed(1)}% 消极${(negative * 100).toFixed(1)}% 中性${(neutral * 100).toFixed(1)}%`);
          
          return { positive, negative, neutral };
          
        } catch (parseError) {
          const errorMessage = parseError instanceof Error ? parseError.message : 'Unknown error';
          console.error(`❌ AI返回内容解析失败: ${errorMessage}`);
          console.error(`原始内容: ${content}`);
          console.error(`内容长度: ${content.length}`);
          
          // 最后的回退：使用关键词匹配
          console.warn('🔄 回退到关键词匹配分析');
          // 恢复原始模型设置
          this.aiService['modelName'] = originalModel;
          return this.fallbackSentimentAnalysis(posts);
        }
      }
      
      // 如果AI分析失败，回退到关键词匹配
      return this.fallbackSentimentAnalysis(posts);
      
    } catch (error) {
      // 恢复原始模型设置
      try {
        this.aiService['modelName'] = originalModel;
      } catch (e) {
        // 忽略恢复错误
      }
      console.warn('AI情感分析失败，使用关键词匹配:', error);
      // AI分析失败时回退到关键词匹配
      return this.fallbackSentimentAnalysis(posts);
    }
  }
  
  // 关键词匹配的回退方案
  private fallbackSentimentAnalysis(posts: XHSPost[]): { positive: number; negative: number; neutral: number } {
    const positiveKeywords = ['好', '棒', '赞', '喜欢', '爱', '优秀', '完美', '推荐', '不错', '很棒', '厉害', '学习', '有用', '感谢', '分享', '支持', '加油', '期待', '好看', '美丽', '精致', '质量好'];
    const negativeKeywords = ['差', '烂', '垃圾', '骗', '假', '失望', '后悔', '不好', '糟糕', '坑', '骗人', '虚假', '夸张', '不推荐', '别买', '踩雷', '质量差', '不值', '浪费钱', '退货', '差评', '吐槽', '无语'];
    
    let positiveCount = 0;
    let negativeCount = 0;
    let neutralCount = 0;
    
    posts.forEach(post => {
      if (post.评论 && Array.isArray(post.评论)) {
        post.评论.forEach(comment => {
          // 确保comment是字符串类型，并进行安全转换
          let commentText = '';
          try {
            if (typeof comment === 'string') {
              commentText = comment;
            } else if (comment !== null && comment !== undefined) {
              commentText = String(comment);
            } else {
              return; // 跳过无效评论
            }
            
            // 清理评论文本格式 (与AI分析中的清理逻辑保持一致)
            if (commentText.startsWith("['") && commentText.endsWith("']")) {
              commentText = commentText.slice(2, -2);
            } else if (commentText.startsWith('["') && commentText.endsWith('"]')) {
              commentText = commentText.slice(2, -2);
            } else if (commentText.startsWith("'") && commentText.endsWith("'")) {
              commentText = commentText.slice(1, -1);
            } else if (commentText.startsWith('"') && commentText.endsWith('"')) {
              commentText = commentText.slice(1, -1);
            }
            
            // 处理特殊情况：只有开头的 [' 但没有结尾的 ']
            if (commentText.startsWith("['") && !commentText.endsWith("']")) {
              commentText = commentText.slice(2);
            }
            
            // 转为小写进行关键词匹配
            commentText = commentText.toLowerCase();
          } catch (err) {
            console.warn(`评论格式转换失败，跳过该评论: ${comment}`, err);
            return; // 跳过有问题的评论
          }
          
          let hasPositive = false;
          let hasNegative = false;
          
          // 检查正面关键词
          for (const keyword of positiveKeywords) {
            if (commentText.includes(keyword)) {
              hasPositive = true;
              break;
            }
          }
          
          // 检查负面关键词
          for (const keyword of negativeKeywords) {
            if (commentText.includes(keyword)) {
              hasNegative = true;
              break;
            }
          }
          
          // 分类统计
          if (hasPositive && !hasNegative) {
            positiveCount++;
          } else if (hasNegative && !hasPositive) {
            negativeCount++;
          } else {
            neutralCount++;
          }
        });
      }
    });
    
    const total = positiveCount + negativeCount + neutralCount;
    
    if (total === 0) {
      return { positive: 0, negative: 0, neutral: 1 };
    }
    
    return {
      positive: positiveCount / total,
      negative: negativeCount / total,
      neutral: neutralCount / total
    };
  }
}