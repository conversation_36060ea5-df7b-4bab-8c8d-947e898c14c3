import { XHSPost, AIAnalysisResult } from '@/types';

export class AIService {
  private apiKey: string;
  private modelName: string;
  private apiEndpoint: string;
  private taskQueue: Array<() => Promise<unknown>> = [];
  private isProcessing = false;

  constructor() {
    this.apiKey = process.env.NEXT_PUBLIC_QWEN_API_KEY || process.env.QWEN_API_KEY || '';
    // 根据分析类型动态选择模型，默认使用qwen-turbo
    this.modelName = process.env.QWEN_MODEL_NAME || 'qwen-turbo';
    this.apiEndpoint = process.env.QWEN_API_ENDPOINT || '';
    
    if (!this.apiKey) {
      console.warn('QWEN_API_KEY environment variable is not set. AI features will be disabled.');
      this.apiKey = 'demo-key'; // 用于演示
    }
  }

  async analyzeContent(posts: XHSPost[], analysisType: 'insight' | 'recommendation' | 'strategy' | 'trend' | 'performance' | 'optimization'): Promise<AIAnalysisResult> {
    const task = async () => {
      const prompt = this.generatePrompt(posts, analysisType);

      console.log(`🤖 开始AI分析 - 类型: ${analysisType}, 数据量: ${posts.length}条`);
      console.log(`🔧 API配置 - 端点: ${this.apiEndpoint}, 模型: ${this.modelName}`);

      // 检查API配置
      if (!this.apiKey || this.apiKey === 'demo-key') {
        console.warn('⚠️ 使用演示模式，返回模拟结果');
        return this.generateDemoResponse(analysisType);
      }

      if (!this.apiEndpoint) {
        throw new Error('API端点未配置');
      }

      try {
        const requestBody = {
          model: this.modelName,
          input: {
            messages: [
              {
                role: 'user',
                content: prompt
              }
            ]
          },
          parameters: {
            temperature: 0.7,
            max_tokens: 2000
          }
        };

        console.log(`📤 发送请求到AI API...`);

        const response = await fetch(this.apiEndpoint, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.apiKey}`,
          },
          body: JSON.stringify(requestBody)
        });

        console.log(`📥 API响应状态: ${response.status} ${response.statusText}`);

        if (!response.ok) {
          const errorText = await response.text();
          console.error(`❌ API请求失败: ${response.status} - ${errorText}`);
          throw new Error(`API request failed: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        console.log(`📊 API返回数据结构:`, Object.keys(data));

        // 修复：正确提取AI返回的内容
        const content = data.output?.choices?.[0]?.message?.content ||
                       data.output?.text ||
                       data.choices?.[0]?.message?.content ||
                       data.text || '';

        console.log(`📝 提取的内容长度: ${content.length}字符`);
        console.log(`📝 内容预览: ${content.substring(0, 200)}...`);

        if (!content) {
          console.error('❌ 未能从API响应中提取内容');
          throw new Error('API返回内容为空');
        }

        return this.parseResponse(content, analysisType);
      } catch (error) {
        console.error('❌ AI analysis error:', error);
        throw error;
      }
    };

    return this.addToQueue(task);
  }

  private generatePrompt(posts: XHSPost[], analysisType: string): string {
    const samplePosts = posts.slice(0, 15);
    const dataSummary = {
      总笔记数: posts.length,
      总点赞数: posts.reduce((sum, post) => sum + post.点赞数, 0),
      总收藏数: posts.reduce((sum, post) => sum + post.收藏数, 0),
      总评论数: posts.reduce((sum, post) => sum + post.评论数, 0),
      平均粉丝数: posts.reduce((sum, post) => sum + post.粉丝, 0) / posts.length
    };

    // 获取所有创作者的详细信息
    const creatorStats = posts.reduce((acc, post) => {
      const author = post.软文作者;
      if (!acc[author]) {
        acc[author] = {
          笔记数: 0,
          总点赞: 0,
          总收藏: 0,
          总评论: 0,
          粉丝数: post.粉丝,
          标题列表: []
        };
      }
      acc[author].笔记数++;
      acc[author].总点赞 += post.点赞数;
      acc[author].总收藏 += post.收藏数;
      acc[author].总评论 += post.评论数;
      acc[author].标题列表.push(post.软文标题);
      return acc;
    }, {} as Record<string, any>);

    const basePrompt = `
你是一位专业的小红书内容营销分析师。请严格基于以下真实数据进行分析，不得编造任何不存在的创作者或数据。

数据概览：
- 总笔记数：${dataSummary.总笔记数}
- 总点赞数：${dataSummary.总点赞数}
- 总收藏数：${dataSummary.总收藏数}
- 总评论数：${dataSummary.总评论数}
- 平均粉丝数：${Math.round(dataSummary.平均粉丝数)}

创作者详细数据：
${Object.entries(creatorStats).map(([author, stats]) => `
创作者：${author}
- 笔记数：${stats.笔记数}
- 总点赞：${stats.总点赞}
- 总收藏：${stats.总收藏}
- 总评论：${stats.总评论}
- 粉丝数：${stats.粉丝数}
- 代表作品：${stats.标题列表.slice(0, 3).join('、')}
`).join('\n')}

样本笔记数据：
${samplePosts.map(post => `
标题：${post.软文标题}
作者：${post.软文作者}
点赞：${post.点赞数}
收藏：${post.收藏数}
评论：${post.评论数}
粉丝：${post.粉丝}
内容：${post.内容 ? post.内容.substring(0, 100) : '无内容'}...
`).join('\n')}

重要提醒：请严格基于上述真实数据进行分析，所有提及的创作者名称必须来自上述数据中的"软文作者"字段，不得编造任何虚假信息。

请以JSON格式返回分析结果，包含以下字段：
`;

    switch (analysisType) {
      case 'insight':
        return basePrompt + `
作为数据洞察专家，请从数据挖掘角度分析小红书内容表现规律。

重点关注：
1. 数据异常值和特殊表现模式
2. 隐藏的用户行为规律
3. 内容表现的深层原因分析
4. 数据背后的用户心理洞察

重要要求：
1. 严格基于上述提供的真实数据进行分析
2. 引用具体的创作者时，必须使用真实的"软文作者"名称
3. 所有数据分析必须有具体的数字支撑
4. 不得编造任何虚假的数据或创作者信息

请严格基于提供的真实数据进行分析，输出格式：
{
  "洞察": "从真实数据中发现的非显而易见的深层规律和异常模式，包括具体的数据证据和背后的原因分析",
  "推荐": ["基于真实数据洞察的内容优化建议（附具体数据支撑）", "用户互动提升策略", "内容发布时机优化建议"],
  "策略": "基于真实数据洞察的整体内容策略，包含数据驱动的决策依据和预期效果"
}`;
      
      case 'recommendation':
        return basePrompt + `
作为KOL合作专家，请从商业合作角度分析创作者价值和合作潜力。

重点关注：
1. 创作者的商业价值评估（ROI潜力）
2. 不同创作者的受众匹配度分析
3. 合作成本效益比较
4. 长期合作价值预测

分析维度：
- 互动质量（评论深度、粉丝活跃度）
- 内容垂直度和专业性
- 商业化潜力和变现能力
- 品牌匹配度和风险评估

重要要求：
1. 只能分析上述"创作者详细数据"中列出的真实创作者
2. 所有推荐的创作者名称必须完全匹配数据中的"软文作者"字段
3. 不得编造任何不存在的创作者信息
4. 基于真实的点赞、收藏、评论、粉丝数据进行分析

请严格基于真实数据分析，输出格式：
{
  "洞察": "基于真实数据的创作者商业价值分析，包括高价值创作者的共同特征和合作优势分析",
  "推荐": ["真实创作者名称1（基于其真实数据的商业价值评估+合作建议）", "真实创作者名称2（基于其真实数据的受众匹配度分析）", "真实创作者名称3（基于其真实数据的成本效益分析）"],
  "策略": "基于真实创作者数据的KOL合作整体策略规划，包含预算分配、合作模式和风险控制"
}`;
      
      case 'strategy':
        return basePrompt + `
作为营销策略专家，请从品牌营销角度制定系统性的小红书营销策略。

重点关注：
1. 品牌定位和差异化策略
2. 目标用户群体细分和触达策略
3. 内容营销矩阵和传播路径设计
4. 营销漏斗优化和转化提升

策略维度：
- 品牌声量提升策略
- 用户心智占领路径
- 竞争优势构建方案
- 营销ROI最大化策略

重要要求：
1. 严格基于上述真实数据制定策略
2. 引用创作者时必须使用真实的"软文作者"名称
3. 所有策略建议必须有具体的数据支撑
4. 不得编造任何虚假信息

请基于真实数据制定可执行的营销策略，输出格式：
{
  "洞察": "基于真实数据的品牌营销机会分析，包括市场空白点、竞争优势和用户需求洞察",
  "推荐": ["基于真实数据的品牌定位策略（差异化方向）", "基于真实创作者数据的用户触达策略（精准营销方案）", "基于真实内容表现的营销矩阵（系统化内容规划）"],
  "策略": "基于真实数据的完整小红书品牌营销策略，包含阶段性目标、执行计划和效果评估体系"
}`;

      case 'trend':
        return basePrompt + `
作为趋势预测专家，请从市场趋势角度分析内容发展方向和机会窗口。

重点关注：
1. 内容趋势的生命周期分析
2. 新兴话题和流行元素识别
3. 用户兴趣迁移和需求变化
4. 市场机会窗口和时机把握

预测维度：
- 内容形式演进趋势
- 用户行为变化趋势
- 平台算法偏好变化
- 商业化机会预测

重要要求：
1. 严格基于上述真实数据进行趋势分析
2. 引用创作者时必须使用真实的"软文作者"名称
3. 所有趋势判断必须有具体的数据支撑
4. 不得编造任何虚假的趋势或数据

请基于真实数据趋势进行前瞻性分析，输出格式：
{
  "洞察": "基于真实数据的内容趋势发展规律分析，包括趋势周期、影响因素和发展预测",
  "推荐": ["基于真实数据的新兴趋势抢占策略（时机判断）", "基于真实内容表现的创新方向建议（差异化机会）", "基于真实互动数据的平台红利把握策略（算法适配建议）"],
  "策略": "基于真实数据的趋势驱动内容规划策略，包含趋势监测体系、快速响应机制和先发优势构建"
}`;

      case 'performance':
        return basePrompt + `
作为效果评估专家，请从投放效果角度分析营销表现和优化机会。

重点关注：
1. 投放效果的量化评估和ROI分析
2. 不同内容类型的转化效率对比
3. 营销漏斗各环节的表现诊断
4. 预算分配效率和优化空间

评估维度：
- 曝光效率和触达质量
- 互动转化和用户留存
- 成本控制和收益最大化
- 长期价值和品牌资产积累

重要要求：
1. 严格基于上述真实数据进行效果评估
2. 引用创作者时必须使用真实的"软文作者"名称
3. 所有效果分析必须有具体的数据支撑
4. 不得编造任何虚假的效果数据

请基于真实数据进行科学的效果评估，输出格式：
{
  "洞察": "基于真实数据的营销效果深度诊断，包括表现亮点、问题识别和效率分析",
  "推荐": ["基于真实数据的投放效率优化建议（具体改进方案）", "基于真实表现的预算分配优化策略（ROI提升路径）", "基于真实转化数据的漏斗优化建议（关键节点改进）"],
  "策略": "基于真实数据的效果驱动营销优化策略，包含KPI体系、监测机制和持续改进计划"
}`;

      case 'optimization':
        return basePrompt + `
作为内容优化专家，请从用户体验角度分析内容改进机会和优化方向。

重点关注：
1. 用户体验痛点和满意度分析
2. 内容质量提升的具体路径
3. 互动体验优化和用户粘性提升
4. 内容生产效率和质量平衡

优化维度：
- 内容结构和表达方式优化
- 视觉呈现和用户界面改进
- 互动机制和参与度提升
- 个性化推荐和精准匹配

重要要求：
1. 严格基于上述真实数据进行优化分析
2. 引用创作者时必须使用真实的"软文作者"名称
3. 所有优化建议必须有具体的数据支撑
4. 不得编造任何虚假的用户反馈或数据

请基于真实用户反馈数据进行优化分析，输出格式：
{
  "洞察": "基于真实数据的用户体验和内容质量深度分析，包括痛点识别、满意度评估和改进机会",
  "推荐": ["基于真实数据的内容质量提升建议（具体改进点）", "基于真实互动数据的用户体验优化策略（互动机制改进）", "基于真实表现的内容生产流程优化（效率与质量平衡）"],
  "策略": "基于真实数据的系统性内容优化策略，包含质量标准、优化流程和效果评估机制"
}`;

      default:
        return basePrompt;
    }
  }

  private generateDemoResponse(analysisType: string): AIAnalysisResult {
    console.log('🎭 生成演示模式响应');

    const demoResponses = {
      insight: {
        洞察: '演示模式：AI分析功能需要配置有效的API密钥才能提供真实的数据洞察。请在环境变量中设置QWEN_API_KEY和QWEN_API_ENDPOINT。',
        推荐: ['配置AI API密钥以获取真实分析', '检查网络连接和API配置', '确保API额度充足'],
        策略: '演示模式下无法提供具体策略建议，请配置AI服务后重试。'
      },
      recommendation: {
        洞察: '演示模式：创作者推荐分析需要AI服务支持。',
        推荐: ['配置AI API以获取创作者价值分析', '检查API配置是否正确'],
        策略: '请配置AI服务以获取个性化推荐策略。'
      },
      strategy: {
        洞察: '演示模式：策略分析需要AI服务支持。',
        推荐: ['配置AI API以获取策略建议', '确保API服务可用'],
        策略: '请配置AI服务以获取营销策略建议。'
      }
    };

    return demoResponses[analysisType as keyof typeof demoResponses] || demoResponses.insight;
  }

  private parseResponse(responseText: string, analysisType: string): AIAnalysisResult {
    try {
      console.log(`🔍 开始解析AI响应，类型: ${analysisType}`);
      console.log(`📝 原始响应长度: ${responseText.length}字符`);

      const jsonMatch = responseText.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        console.error('❌ 响应中未找到JSON格式数据');
        console.log(`📝 响应内容: ${responseText}`);
        throw new Error('No JSON found in response');
      }

      const jsonStr = jsonMatch[0];
      console.log(`📊 提取的JSON: ${jsonStr.substring(0, 500)}...`);

      const parsed = JSON.parse(jsonStr);
      console.log('✅ JSON解析成功:', parsed);

      const result = {
        洞察: typeof parsed.洞察 === 'string' ? parsed.洞察 : (parsed.洞察 ? JSON.stringify(parsed.洞察) : ''),
        推荐: Array.isArray(parsed.推荐) ? parsed.推荐 : (parsed.推荐 ? [String(parsed.推荐)] : []),
        策略: typeof parsed.策略 === 'string' ? parsed.策略 : (parsed.策略 ? JSON.stringify(parsed.策略) : '')
      };

      console.log('🎯 最终解析结果:', result);
      return result;
    } catch (error) {
      console.error('❌ AI响应解析失败:', error);
      console.log(`📝 原始响应: ${responseText}`);
      return {
        洞察: `分析结果解析失败: ${error instanceof Error ? error.message : '未知错误'}`,
        推荐: ['请检查AI服务配置', '重试分析请求'],
        策略: '请重新配置AI服务或联系技术支持'
      };
    }
  }

  private async addToQueue<T>(task: () => Promise<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      this.taskQueue.push(async () => {
        try {
          const result = await task();
          resolve(result);
        } catch (error) {
          reject(error);
        }
      });
      
      if (!this.isProcessing) {
        this.processQueue();
      }
    });
  }

  private async processQueue() {
    if (this.isProcessing || this.taskQueue.length === 0) {
      return;
    }

    this.isProcessing = true;
    
    while (this.taskQueue.length > 0) {
      const task = this.taskQueue.shift()!;
      try {
        await task();
      } catch (error) {
        console.error('Task execution failed:', error);
      }
      
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    this.isProcessing = false;
  }
}