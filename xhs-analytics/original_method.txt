  private async analyzeCommentSentiment(posts: XHSPost[]): Promise<{ positive: number; negative: number; neutral: number }> {
    const allComments = posts.flatMap(post => post.评论 || []);
    
    console.log('🔍 analyzeCommentSentiment 调试信息:');
    console.log('- 处理的posts数量:', posts.length);
    console.log('- 提取的comments数量:', allComments.length);
    console.log('- 前3条评论:', allComments.slice(0, 3));
    console.log('');
    
    if (allComments.length === 0) {
      console.log('⚠️ 评论为空，返回默认值');
      return { positive: 0, negative: 0, neutral: 1 };
    }
    
    // 🔧 简化评论处理：统一转为字符串，并清理格式
    const cleanedComments = allComments.map(comment => {
      // 统一转为字符串
      let commentStr = String(comment || '').trim();
      
      // 清理各种格式问题
      // 移除数组括号和引号：['评论'] → 评论
      if (commentStr.startsWith("['") && commentStr.endsWith("']")) {
        commentStr = commentStr.slice(2, -2);
      } else if (commentStr.startsWith('["') && commentStr.endsWith('"]')) {
        commentStr = commentStr.slice(2, -2);
      } else if (commentStr.startsWith("'") && commentStr.endsWith("'")) {
        commentStr = commentStr.slice(1, -1);
      } else if (commentStr.startsWith('"') && commentStr.endsWith('"')) {
        commentStr = commentStr.slice(1, -1);
      }
      
      // 处理不完整的格式，如 "['好香'" 或 "'好香" 等
      if (commentStr.startsWith("['")) {
        commentStr = commentStr.slice(2); // 移除 ['
      } else if (commentStr.startsWith("'")) {
        commentStr = commentStr.slice(1); // 移除 '
      } else if (commentStr.startsWith('"')) {
        commentStr = commentStr.slice(1); // 移除 "
      }
      
      if (commentStr.endsWith("']")) {
        commentStr = commentStr.slice(0, -2); // 移除 ']
      } else if (commentStr.endsWith("'")) {
        commentStr = commentStr.slice(0, -1); // 移除 '
      } else if (commentStr.endsWith('"')) {
        commentStr = commentStr.slice(0, -1); // 移除 "
      }
      
      return commentStr;
    }).filter(comment => comment.length > 0);
    
    console.log('🧹 清理后的comments数量:', cleanedComments.length);
    console.log('🧹 清理后的前3条评论:', cleanedComments.slice(0, 3));
    console.log('');
    
    // 临时使用更快的模型进行情感分析
    const originalModel = this.aiService['modelName'];
    this.aiService['modelName'] = 'qwen-turbo'; // 使用更快的模型
    
    try {
      // 使用现有的AI服务进行情感分析，使用更快的模型
      const sentimentPrompt = `请分析以下小红书评论的情感倾向，按照严格的JSON格式返回结果。

评论内容：
${cleanedComments.slice(0, 30).join('\n')}

请务必严格按照以下JSON格式返回，不要添加任何解释文字、markdown标记或其他内容：
{
  "positive": 积极评论数量,
  "negative": 消极评论数量,
  "neutral": 中性评论数量,
  "total": 总评论数,
  "positiveRatio": 积极比例(0到1之间的小数),
  "negativeRatio": 消极比例(0到1之间的小数),
  "neutralRatio": 中性比例(0到1之间的小数)
}

分析标准：
- 积极：好、棒、赞、喜欢、推荐、不错、很棒等正面表达
- 消极：差、烂、不好、失望、不推荐、质量差等负面表达
- 中性：一般描述、事实陈述、无明显情感倾向

要求：只返回纯JSON格式，不要添加任何其他文字。`;

      console.log('📝 发送的提示词长度:', sentimentPrompt.length);
      console.log('📝 提示词前100字符:', sentimentPrompt.substring(0, 100));
      console.log('');
      
      const response = await fetch(this.aiService['apiEndpoint'], {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.aiService['apiKey']}`
        },
        body: JSON.stringify({
          model: this.aiService['modelName'],
          input: {
            messages: [{
              role: 'user',
              content: sentimentPrompt
            }]
          },
          parameters: {
            result_format: 'message',
            temperature: 0.1, // 降低随机性，提高JSON格式稳定性
            top_p: 0.8,
            max_tokens: 500
          }
        })
      });

      if (!response.ok) {
        const errorInfo = `❌ AI API调用失败: ${response.status} ${response.statusText}`;
        console.error(errorInfo);
        throw new Error(errorInfo);
      }

      const result = await response.json();
      console.log(`📤 AI API完整响应:`);
      console.log(JSON.stringify(result, null, 2));
      
      const content = result.output?.choices?.[0]?.message?.content;
      
      if (!content) {
        const errorInfo = `❌ AI返回内容为空: ${JSON.stringify(result)}`;
        console.error(errorInfo);
        throw new Error(errorInfo);
      }
      
      if (content) {
        try {
          // 🔍 调试：打印AI返回的原始内容
          console.log(`=== AI情感分析返回内容 ===`);
          console.log(`原始内容: ${content}`);
          console.log(`内容类型: ${typeof content}`);
          console.log(`内容长度: ${content.length}`);
          
          // 数据清理：移除可能的问题字符
          let cleanedContent = content.trim();
          
          // 移除可能的BOM标记
          cleanedContent = cleanedContent.replace(/^\uFEFF/, '');
          
          // 修复1：处理markdown代码块格式
          const jsonMatch = cleanedContent.match(/```json\s*([\s\S]*?)\s*```/);
          if (jsonMatch) {
            cleanedContent = jsonMatch[1].trim();
            console.log('📝 提取markdown代码块:', cleanedContent);
          }
          
          // 修复2：移除前后引号
          if (cleanedContent.startsWith('"') && cleanedContent.endsWith('"')) {
            cleanedContent = cleanedContent.slice(1, -1);
            console.log('📝 移除前后引号:', cleanedContent);
          }
          
          // 修复3：处理转义字符
          cleanedContent = cleanedContent.replace(/\\"/g, '"').replace(/\\n/g, '\n');
          
          // 修复4：如果内容不是以{开头，尝试寻找JSON部分
          if (!cleanedContent.startsWith('{')) {
            const jsonStart = cleanedContent.indexOf('{');
            const jsonEnd = cleanedContent.lastIndexOf('}');
            if (jsonStart !== -1 && jsonEnd !== -1 && jsonEnd > jsonStart) {
              cleanedContent = cleanedContent.substring(jsonStart, jsonEnd + 1);
              console.log('📝 提取JSON部分:', cleanedContent);
            }
          }
          
          console.log('🔧 最终清理后的内容:', cleanedContent);
          
          // 尝试解析JSON
          const sentimentResult = JSON.parse(cleanedContent);
          console.log(`✅ JSON解析成功:`, sentimentResult);
          
          // 恢复原始模型设置
          this.aiService['modelName'] = originalModel;
          
          // 计算比例，支持多种返回格式
          let positive = 0, negative = 0, neutral = 0;
          
          if (sentimentResult.positiveRatio !== undefined) {
            // 如果AI直接返回比例
            positive = sentimentResult.positiveRatio;
            negative = sentimentResult.negativeRatio || 0;
            neutral = sentimentResult.neutralRatio || 0;
          } else if (sentimentResult.positive !== undefined && sentimentResult.total !== undefined) {
            // 如果AI返回数量和总数
            const total = sentimentResult.total || (sentimentResult.positive + sentimentResult.negative + sentimentResult.neutral);
            positive = total > 0 ? sentimentResult.positive / total : 0;
            negative = total > 0 ? (sentimentResult.negative || 0) / total : 0;
            neutral = total > 0 ? (sentimentResult.neutral || 0) / total : 1;
          } else {
            // 如果只返回数量，计算比例
            const total = (sentimentResult.positive || 0) + (sentimentResult.negative || 0) + (sentimentResult.neutral || 0);
            positive = total > 0 ? (sentimentResult.positive || 0) / total : 0;
            negative = total > 0 ? (sentimentResult.negative || 0) / total : 0;
            neutral = total > 0 ? (sentimentResult.neutral || 0) / total : 1;
          }
          
          // 确保比例总和为1
          const sum = positive + negative + neutral;
          if (sum > 0) {
            positive = positive / sum;
            negative = negative / sum;
            neutral = neutral / sum;
          }
          
          console.log(`📊 最终情感分析结果: 积极${(positive * 100).toFixed(1)}% 消极${(negative * 100).toFixed(1)}% 中性${(neutral * 100).toFixed(1)}%`);
          
          return { positive, negative, neutral };
          
        } catch (parseError) {
          const errorMessage = parseError instanceof Error ? parseError.message : 'Unknown error';
          console.error(`❌ AI返回内容解析失败: ${errorMessage}`);
          console.error(`原始内容: ${content}`);
          console.error(`内容长度: ${content.length}`);
          
          // 最后的回退：使用关键词匹配
          console.warn('🔄 回退到关键词匹配分析');
          // 恢复原始模型设置
          this.aiService['modelName'] = originalModel;
          return this.fallbackSentimentAnalysis(posts);
        }
      }
      
      // 如果AI分析失败，回退到关键词匹配
      return this.fallbackSentimentAnalysis(posts);
      
    } catch (error) {
      // 恢复原始模型设置
      try {
        this.aiService['modelName'] = originalModel;
      } catch (e) {
        // 忽略恢复错误
      }
      console.warn('AI情感分析失败，使用关键词匹配:', error);
      // AI分析失败时回退到关键词匹配
      return this.fallbackSentimentAnalysis(posts);
    }
  }
  
  // 关键词匹配的回退方案
  private fallbackSentimentAnalysis(posts: XHSPost[]): { positive: number; negative: number; neutral: number } {
