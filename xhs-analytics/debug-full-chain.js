// 全链路调试：检查AI情感分析的每个步骤
require('dotenv').config();

const API_KEY = process.env.QWEN_API_KEY || 'sk-676513654fba4a318e64501772deae87';
const MODEL_NAME = 'qwen-turbo'; // 使用更快的模型
const API_ENDPOINT = process.env.QWEN_API_ENDPOINT || 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation';

// 模拟真实的评论数据（包含可能的特殊字符）
const realComments = [
  '这个产品真的很好用，强烈推荐给大家！',
  '质量一般，价格有点贵，不太值得购买',
  '绝了！yyds！质量太赞了！',
  '笑死，这什么玩意儿，完全没用',
  '还可以吧，没有特别的感觉',
  '破防了，这质量也太差了吧',
  '挺好的，符合预期，会继续购买',
  '无语了，这钱花得太冤枉了',
  '精致好看，质量很棒，很喜欢！',
  '踩雷了，大家别买，真的垃圾',
  '哈哈哈哈哈笑死我了这个产品太搞笑了',
  '嗯...感觉一般般吧，没有特别惊艳',
  '👍👍👍强烈推荐！买了不后悔！',
  '💔💔💔太失望了，完全不值这个价',
  '😊😊😊很满意，会回购的',
  '😡😡😡气死我了，被骗了！'
];

async function debugFullChain() {
  console.log('=== 全链路调试AI情感分析 ===');
  console.log('API Key:', API_KEY ? '已设置' : '未设置');
  console.log('模型:', MODEL_NAME);
  console.log('接口:', API_ENDPOINT);
  console.log('');

  // 步骤1：构建提示词
  console.log('📝 步骤1：构建提示词');
  const sentimentPrompt = `请分析以下小红书评论的情感倾向，统计积极、消极、中性的数量和比例。

评论内容：
${realComments.slice(0, 10).join('\n')}

请严格按照以下JSON格式返回，不要添加任何其他文字或解释：
{
  "positive": 积极评论数量,
  "negative": 消极评论数量, 
  "neutral": 中性评论数量,
  "total": 总评论数,
  "positiveRatio": 积极比例(0-1之间的小数),
  "negativeRatio": 消极比例(0-1之间的小数),
  "neutralRatio": 中性比例(0-1之间的小数)
}

分析标准：
- 积极：表达满意、喜欢、推荐、赞扬等正面情绪
- 消极：表达不满、失望、批评、抱怨等负面情绪  
- 中性：客观描述、事实陈述、无明显情感倾向`;

  console.log('提示词构建完成');
  console.log('');

  // 步骤2：API调用
  console.log('🌐 步骤2：API调用');
  try {
    const response = await fetch(API_ENDPOINT, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${API_KEY}`
      },
      body: JSON.stringify({
        model: MODEL_NAME,
        input: {
          messages: [{
            role: 'user',
            content: sentimentPrompt
          }]
        },
        parameters: {
          result_format: 'message'
        }
      })
    });

    console.log('HTTP响应状态:', response.status, response.statusText);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ API调用失败:', errorText);
      return;
    }

    // 步骤3：解析响应
    console.log('📦 步骤3：解析响应');
    const result = await response.json();
    console.log('✅ API响应解析成功');
    console.log('');

    // 步骤4：提取内容
    console.log('📄 步骤4：提取内容');
    const content = result.output?.choices?.[0]?.message?.content;
    
    if (!content) {
      console.error('❌ AI返回内容为空');
      console.log('完整响应结构:', JSON.stringify(result, null, 2));
      return;
    }

    console.log('✅ 内容提取成功');
    console.log('原始内容类型:', typeof content);
    console.log('原始内容长度:', content.length);
    console.log('');

    // 步骤5：详细内容分析
    console.log('🔍 步骤5：详细内容分析');
    console.log('=== 原始内容 ===');
    console.log(content);
    console.log('');

    // 检查内容特征
    console.log('=== 内容特征分析 ===');
    console.log('是否以```json开头:', content.trim().startsWith('```json'));
    console.log('是否以```结尾:', content.trim().endsWith('```'));
    console.log('是否包含BOM标记:', content.charCodeAt(0) === 0xFEFF);
    console.log('是否以引号开头:', content.trim().startsWith('"'));
    console.log('是否以引号结尾:', content.trim().endsWith('"'));
    console.log('是否包含换行符:', content.includes('\n'));
    console.log('是否包含制表符:', content.includes('\t'));
    console.log('');

    // 步骤6：数据清理
    console.log('🧹 步骤6：数据清理');
    let cleanedContent = content.trim();
    console.log('修剪后内容:', cleanedContent);
    console.log('');

    // 清理步骤1：移除BOM标记
    if (cleanedContent.charCodeAt(0) === 0xFEFF) {
      cleanedContent = cleanedContent.substring(1);
      console.log('✅ 移除BOM标记:', cleanedContent);
    }

    // 清理步骤2：处理markdown代码块
    const jsonMatch = cleanedContent.match(/```json\s*([\s\S]*?)\s*```/);
    if (jsonMatch) {
      cleanedContent = jsonMatch[1].trim();
      console.log('✅ 提取markdown代码块:', cleanedContent);
    }

    // 清理步骤3：移除前后引号
    if (cleanedContent.startsWith('"') && cleanedContent.endsWith('"')) {
      cleanedContent = cleanedContent.slice(1, -1);
      console.log('✅ 移除前后引号:', cleanedContent);
    }

    console.log('最终清理后的内容:', cleanedContent);
    console.log('');

    // 步骤7：JSON解析尝试
    console.log('🔧 步骤7：JSON解析尝试');
    console.log('尝试解析的内容:', cleanedContent);
    console.log('');

    try {
      const sentimentResult = JSON.parse(cleanedContent);
      console.log('✅ JSON解析成功!');
      console.log('解析结果:', sentimentResult);
      console.log('');

      // 验证数据结构
      console.log('📊 步骤8：数据验证');
      const requiredFields = ['positive', 'negative', 'neutral', 'total'];
      const missingFields = requiredFields.filter(field => !(field in sentimentResult));
      
      if (missingFields.length > 0) {
        console.error('❌ 缺少必要字段:', missingFields);
        return;
      }

      console.log('✅ 数据结构验证通过');
      console.log('');

      // 计算最终结果
      console.log('🎯 步骤9：计算最终结果');
      const total = sentimentResult.total || sentimentResult.positive + sentimentResult.negative + sentimentResult.neutral;
      const positiveRatio = sentimentResult.positiveRatio || sentimentResult.positive / total;
      const negativeRatio = sentimentResult.negativeRatio || sentimentResult.negative / total;
      const neutralRatio = sentimentResult.neutralRatio || sentimentResult.neutral / total;

      console.log('=== 最终结果 ===');
      console.log('积极评论数:', sentimentResult.positive);
      console.log('消极评论数:', sentimentResult.negative);
      console.log('中性评论数:', sentimentResult.neutral);
      console.log('总评论数:', total);
      console.log('积极比例:', positiveRatio.toFixed(3));
      console.log('消极比例:', negativeRatio.toFixed(3));
      console.log('中性比例:', neutralRatio.toFixed(3));
      console.log('');

      console.log('🎉 全链路调试成功！');

    } catch (parseError) {
      console.error('❌ JSON解析失败:', parseError.message);
      console.error('失败位置:', parseError);
      
      // 尝试更详细的错误分析
      console.log('');
      console.log('🔍 错误详细分析');
      console.log('尝试解析的内容长度:', cleanedContent.length);
      console.log('尝试解析的内容前50字符:', cleanedContent.substring(0, 50));
      console.log('尝试解析的内容后50字符:', cleanedContent.substring(cleanedContent.length - 50));
      
      // 尝试字符转义
      const escapedContent = cleanedContent
        .replace(/\n/g, '\n')
        .replace(/\r/g, '\r')
        .replace(/\t/g, '\t')
        .replace(/"/g, '\"');
      
      console.log('转义后的内容:', escapedContent);
      
      // 尝试手动修复
      try {
        // 修复常见问题：省略小数点前的0
        let fixedContent = cleanedContent.replace(/:\s*\.(\d+)/g, ': 0.$1');
        
        // 修复缺少引号的问题
        fixedContent = fixedContent.replace(/([{,]\s*)([a-zA-Z_]+)(\s*):/g, '$1"$2"$3:');
        
        console.log('尝试修复后的内容:', fixedContent);
        
        const fixedResult = JSON.parse(fixedContent);
        console.log('✅ 修复后JSON解析成功:', fixedResult);
        
      } catch (secondError) {
        console.error('❌ 修复后仍然解析失败:', secondError.message);
      }
    }

  } catch (error) {
    console.error('❌ 全链路调试失败:', error.message);
  }
}

// 运行全链路调试
debugFullChain();
