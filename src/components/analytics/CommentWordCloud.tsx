'use client';

import React, { useEffect, useRef, useState } from 'react';
import { XHSPost } from '@/types';

interface WordCloudProps {
  posts: XHSPost[];
  sentimentType: 'positive' | 'negative' | 'neutral' | 'all';
}

interface WordFrequency {
  text: string;
  size: number;
  sentiment: 'positive' | 'negative' | 'neutral';
}

export function CommentWordCloud({ posts, sentimentType }: WordCloudProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [wordData, setWordData] = useState<WordFrequency[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // 情感关键词库
  const sentimentKeywords = {
    positive: ['好', '棒', '赞', '喜欢', '爱', '优秀', '完美', '推荐', '不错', '很棒', '厉害', '学习', '有用', '感谢', '分享', '支持', '加油', '期待', '好看', '美丽', '精致', '质量好', '值得', '满意', '开心', '惊喜', '超级', '太好了', '必须', '收藏', '种草'],
    negative: ['差', '烂', '垃圾', '骗', '假', '失望', '后悔', '不好', '糟糕', '坑', '骗人', '虚假', '夸张', '不推荐', '别买', '踩雷', '质量差', '不值', '浪费钱', '退货', '差评', '吐槽', '无语', '坑爹', '难吃', '难看', '太贵', '不划算'],
    neutral: ['一般', '还行', '可以', '普通', '正常', '平常', '中等', '凑合', '马马虎虎']
  };

  // 停用词
  const stopWords = new Set([
    '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好的', '这个', '那个', '什么', '怎么', '为什么', '因为', '所以', '但是', '如果', '虽然', '然后', '还有', '或者', '可能', '应该', '已经', '正在', '可以', '需要', '想要', '知道', '觉得', '认为', '希望', '喜欢', '讨厌', '同意', '反对', '支持', '理解', '明白', '清楚', '确定', '肯定', '绝对', '完全', '非常', '特别', '尤其', '特殊', '普通', '一般', '基本', '主要', '重要', '关键', '核心', '中心', '焦点', '重点', '难点', '问题', '方法', '方式', '途径', '手段', '工具', '设备', '材料', '内容', '信息', '数据', '结果', '效果', '影响', '作用', '意义', '价值', '利益', '好处', '坏处', '优点', '缺点', '特点', '特色', '特征', '性质', '属性', '功能', '作用', '用途', '目的', '意图', '计划', '安排', '准备', '开始', '结束', '完成', '成功', '失败', '胜利', '失败', '进步', '发展', '变化', '改变', '提高', '增加', '减少', '保持', '维持', '继续', '停止', '暂停', '延迟', '提前', '及时', '准时', '迟到', '早到', '到达', '离开', '回来', '出去', '进来', '上去', '下来', '过去', '过来', '起来', '下去', '进去', '出来', '回去', '过来', '起来', '下去', '进去', '出来', '回去'
  ]);

  // 分析评论情感并提取关键词
  const analyzeComments = async () => {
    setIsLoading(true);
    
    try {
      // 收集所有评论
      const allComments: string[] = [];
      posts.forEach(post => {
        if (post.评论 && Array.isArray(post.评论)) {
          allComments.push(...post.评论);
        }
      });

      if (allComments.length === 0) {
        setWordData([]);
        setIsLoading(false);
        return;
      }

      // 分词和情感分析
      const wordFreqMap = new Map<string, { count: number; sentiment: 'positive' | 'negative' | 'neutral' }>();

      allComments.forEach(comment => {
        // 过滤None值、空值和无效数据
        if (!comment ||
            typeof comment !== 'string' ||
            comment === 'None' ||
            comment === 'null' ||
            comment === 'undefined' ||
            comment.toLowerCase() === 'none' ||
            comment.toLowerCase() === 'null') return;

        const cleanComment = comment.trim();
        if (cleanComment.length === 0 ||
            cleanComment === 'None' ||
            cleanComment === 'null' ||
            cleanComment.toLowerCase() === 'none' ||
            cleanComment.toLowerCase() === 'null') return;

        // 简单分词（按字符和标点分割）
        const words = cleanComment
          .replace(/[^\u4e00-\u9fa5a-zA-Z0-9]/g, ' ')
          .split(/\s+/)
          .filter(word =>
            word.length >= 2 &&
            !stopWords.has(word) &&
            word !== 'None' &&
            word !== 'null' &&
            word.toLowerCase() !== 'none' &&
            word.toLowerCase() !== 'null'
          );

        words.forEach(word => {
          // 判断词汇情感
          let sentiment: 'positive' | 'negative' | 'neutral' = 'neutral';
          
          if (sentimentKeywords.positive.some(keyword => word.includes(keyword) || keyword.includes(word))) {
            sentiment = 'positive';
          } else if (sentimentKeywords.negative.some(keyword => word.includes(keyword) || keyword.includes(word))) {
            sentiment = 'negative';
          }

          const key = word;
          if (wordFreqMap.has(key)) {
            const existing = wordFreqMap.get(key)!;
            existing.count++;
            // 如果有明确情感，更新情感标签
            if (sentiment !== 'neutral') {
              existing.sentiment = sentiment;
            }
          } else {
            wordFreqMap.set(key, { count: 1, sentiment });
          }
        });
      });

      // 转换为词云数据
      let wordArray = Array.from(wordFreqMap.entries())
        .map(([text, data]) => ({
          text,
          size: data.count,
          sentiment: data.sentiment
        }))
        .filter(item => item.size >= 2) // 过滤出现次数少的词
        .sort((a, b) => {
          // 情感优先级排序：负面 > 正面 > 中性，同情感按频率排序
          if (a.sentiment !== b.sentiment) {
            const sentimentPriority = { 'negative': 3, 'positive': 2, 'neutral': 1 };
            return sentimentPriority[b.sentiment] - sentimentPriority[a.sentiment];
          }
          return b.size - a.size;
        });

      // 根据情感类型过滤
      if (sentimentType !== 'all') {
        wordArray = wordArray.filter(item => item.sentiment === sentimentType);
      } else {
        // 在"全部"模式下，确保负面情感词汇优先显示
        // 先取负面词汇，再取正面词汇，最后取中性词汇
        const negativeWords = wordArray.filter(item => item.sentiment === 'negative').slice(0, 20);
        const positiveWords = wordArray.filter(item => item.sentiment === 'positive').slice(0, 20);
        const neutralWords = wordArray.filter(item => item.sentiment === 'neutral').slice(0, 10);

        wordArray = [...negativeWords, ...positiveWords, ...neutralWords];
      }

      // 取前50个词（如果不是全部模式）
      if (sentimentType !== 'all') {
        wordArray = wordArray.slice(0, 50);
      }

      // 标准化大小（10-60之间）
      if (wordArray.length > 0) {
        const maxSize = wordArray[0].size;
        const minSize = wordArray[wordArray.length - 1].size;
        const sizeRange = maxSize - minSize || 1;
        
        wordArray = wordArray.map(item => ({
          ...item,
          size: Math.max(10, Math.min(60, 10 + (item.size - minSize) / sizeRange * 50))
        }));
      }

      setWordData(wordArray);
    } catch (error) {
      console.error('词云分析失败:', error);
      setWordData([]);
    } finally {
      setIsLoading(false);
    }
  };

  // 绘制词云
  const drawWordCloud = () => {
    const canvas = canvasRef.current;
    if (!canvas || wordData.length === 0) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // 清空画布
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // 设置画布大小
    const rect = canvas.getBoundingClientRect();
    canvas.width = rect.width * window.devicePixelRatio;
    canvas.height = rect.height * window.devicePixelRatio;
    ctx.scale(window.devicePixelRatio, window.devicePixelRatio);

    const centerX = rect.width / 2;
    const centerY = rect.height / 2;

    // 绘制词汇
    wordData.forEach((word, index) => {
      // 根据情感设置颜色
      let color = '#666666'; // 默认中性色
      if (word.sentiment === 'positive') {
        color = '#10B981'; // 绿色
      } else if (word.sentiment === 'negative') {
        color = '#EF4444'; // 红色
      }

      ctx.fillStyle = color;
      ctx.font = `${word.size}px Arial`;
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';

      // 简单的螺旋布局
      const angle = (index * 137.5) * (Math.PI / 180); // 黄金角度
      const radius = Math.sqrt(index) * 20;
      const x = centerX + Math.cos(angle) * radius;
      const y = centerY + Math.sin(angle) * radius;

      ctx.fillText(word.text, x, y);
    });
  };

  useEffect(() => {
    analyzeComments();
  }, [posts, sentimentType]);

  useEffect(() => {
    if (!isLoading && wordData.length > 0) {
      drawWordCloud();
    }
  }, [wordData, isLoading]);

  const getSentimentLabel = () => {
    switch (sentimentType) {
      case 'positive': return '积极评论';
      case 'negative': return '消极评论';
      case 'neutral': return '中性评论';
      default: return '全部评论';
    }
  };

  const getSentimentColor = () => {
    switch (sentimentType) {
      case 'positive': return 'text-green-600';
      case 'negative': return 'text-red-600';
      case 'neutral': return 'text-gray-600';
      default: return 'text-blue-600';
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">正在分析评论词汇...</div>
      </div>
    );
  }

  if (wordData.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">暂无{getSentimentLabel()}数据</div>
      </div>
    );
  }

  return (
    <div className="w-full">
      <div className="mb-4">
        <h4 className={`text-lg font-medium ${getSentimentColor()}`}>
          {getSentimentLabel()}词云 ({wordData.length}个关键词)
        </h4>
        <p className="text-sm text-gray-500 mt-1">
          词汇大小表示出现频率，颜色表示情感倾向
        </p>
      </div>
      <canvas
        ref={canvasRef}
        className="w-full h-64 border border-gray-200 rounded-lg"
        style={{ width: '100%', height: '256px' }}
      />
      
      {/* 词频统计 */}
      <div className="mt-4 grid grid-cols-2 md:grid-cols-4 gap-2 text-xs">
        {wordData.slice(0, 8).map((word, index) => (
          <div key={index} className="flex justify-between items-center p-2 bg-gray-50 rounded">
            <span className={word.sentiment === 'positive' ? 'text-green-600' : 
                           word.sentiment === 'negative' ? 'text-red-600' : 'text-gray-600'}>
              {word.text}
            </span>
            <span className="text-gray-500">{Math.round(word.size)}</span>
          </div>
        ))}
      </div>
    </div>
  );
}

export default CommentWordCloud;
